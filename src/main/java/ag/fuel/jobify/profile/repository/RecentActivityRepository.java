package ag.fuel.jobify.profile.repository;

import ag.fuel.jobify.profile.entity.RecentActivity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RecentActivityRepository extends JpaRepository<RecentActivity, Long> {

    @Query("SELECT ra FROM RecentActivity ra WHERE ra.userId = :userId ORDER BY ra.id DESC")
    Page<RecentActivity> findByUserIdOrderByIdDesc(@Param("userId") Long userId, Pageable pageable);

    @Query("SELECT ra FROM RecentActivity ra WHERE ra.userId = :userId ORDER BY ra.id DESC")
    List<RecentActivity> findByUserIdOrderByIdDesc(@Param("userId") Long userId);

    @Query("SELECT ra FROM RecentActivity ra WHERE ra.userId = :userId " +
           "AND (:dateFilter IS NULL OR ra.dateTime LIKE CONCAT(:dateFilter, '%')) " +
           "AND (:activityFilter IS NULL OR ra.activity = :activityFilter) " +
           "ORDER BY ra.id DESC")
    Page<RecentActivity> findByUserIdWithFiltersOrderByIdDesc(
            @Param("userId") Long userId,
            @Param("dateFilter") String dateFilter,
            @Param("activityFilter") String activityFilter,
            Pageable pageable);

    @Query("SELECT DISTINCT ra.activity FROM RecentActivity ra WHERE ra.userId = :userId AND ra.activity IS NOT NULL ORDER BY ra.activity")
    List<String> findDistinctActivitiesByUserId(@Param("userId") Long userId);
}
