package ag.fuel.jobify.company.controller;

import ag.fuel.jobify.company.entity.Company;
import ag.fuel.jobify.company.service.CompanyService;
import ag.fuel.jobify.user.dto.UserListDto;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/admin/companies")
@RequiredArgsConstructor
public class CompanyUsersController {

    private final CompanyService companyService;
    private final UserService userService;

    /**
     * Get users for a specific company with pagination
     */
    @GetMapping("/{id}/users")
    public String getCompanyUsers(
            @PathVariable Long id,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            Model model) {
        Company company = companyService.getCompanyById(id)
                .orElseThrow(() -> new IllegalArgumentException("Company not found"));

        // Get paginated users with this company name
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<User> userPage = userService.getUsersWithFilters(
                null, // fullName
                null, // email
                company.getCompanyName(), // company
                null, // role
                null, // enabled
                null, // accountLocked
                pageable);

        // Convert to DTOs
        Page<UserListDto> userDtoPage = userPage.map(userService::toUserListDto);

        model.addAttribute("users", userDtoPage);
        model.addAttribute("companyId", company.getId());
        model.addAttribute("companyName", company.getCompanyName());
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", userDtoPage.getTotalPages());

        return "company/fragments/company-users :: companyUsersList";
    }

    /**
     * Search users within a company with pagination
     */
    @PostMapping("/users/search")
    public String searchCompanyUsers(
            @RequestParam Long companyId,
            @RequestParam(required = false, defaultValue = "") String userSearchInput,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            Model model) {
        Company company = companyService.getCompanyById(companyId)
                .orElseThrow(() -> new IllegalArgumentException("Company not found"));

        // Get paginated users with this company name that match the search term
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<User> userPage = userService.getUsersWithFilters(
                userSearchInput, // fullName (search in name)
                userSearchInput, // email (search in email)
                company.getCompanyName(), // company
                null, // role
                null, // enabled
                null, // accountLocked
                pageable);

        // Convert to DTOs
        Page<UserListDto> userDtoPage = userPage.map(userService::toUserListDto);

        model.addAttribute("users", userDtoPage);
        model.addAttribute("companyId", company.getId());
        model.addAttribute("companyName", company.getCompanyName());
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", userDtoPage.getTotalPages());
        model.addAttribute("searchTerm", userSearchInput);

        return "company/fragments/company-users :: companyUsersList";
    }

    /**
     * Get users available to add to a company with pagination
     */
    @GetMapping("/users/available")
    public String getAvailableUsers(
            @RequestParam Long companyId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false, defaultValue = "") String searchInput,
            Model model) {
        Company company = companyService.getCompanyById(companyId)
                .orElseThrow(() -> new IllegalArgumentException("Company not found"));

        // Get users not in this company with pagination
        Pageable pageable = PageRequest.of(page - 1, size);

        // Get users that don't belong to this company and match the search criteria
        Page<User> availableUsersPage = userService.getUsersNotInCompany(
                company.getCompanyName(), // companyName (exclude users with this company)
                searchInput.isEmpty() ? null : searchInput, // fullName (search in name)
                searchInput.isEmpty() ? null : searchInput, // email (search in email)
                null, // role
                null, // enabled
                null, // accountLocked
                pageable);

        // Convert to DTOs
        Page<UserListDto> availableUsersDtoPage = availableUsersPage.map(userService::toUserListDto);

        model.addAttribute("availableUsers", availableUsersDtoPage);
        model.addAttribute("companyId", company.getId());
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", availableUsersDtoPage.getTotalPages());
        model.addAttribute("searchTerm", searchInput);

        return "company/fragments/available-users :: availableUsersList";
    }

    /**
     * Add users to a company
     */
    @PostMapping("/users/add")
    public String addUsersToCompany(
            @RequestParam Long companyId,
            @RequestParam(required = false) List<Integer> usersToAdd,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            Model model) {
        if (usersToAdd == null || usersToAdd.isEmpty()) {
            return getCompanyUsers(companyId, 1, 10, model);
        }

        Company company = companyService.getCompanyById(companyId)
                .orElseThrow(() -> new IllegalArgumentException("Company not found"));

        // Update each user's company
        for (Integer userId : usersToAdd) {
            userService.findById(userId).ifPresent(user -> {
                user.setCompany(company.getCompanyName());
                userService.saveUser(user);
            });
        }

        // Get paginated users with this company name
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<User> userPage = userService.getUsersWithFilters(
                null, // fullName
                null, // email
                company.getCompanyName(), // company
                null, // role
                null, // enabled
                null, // accountLocked
                pageable);

        // Convert to DTOs
        Page<UserListDto> userDtoPage = userPage.map(userService::toUserListDto);

        model.addAttribute("users", userDtoPage);
        model.addAttribute("companyId", company.getId());
        model.addAttribute("companyName", company.getCompanyName());
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", userDtoPage.getTotalPages());

        return "company/fragments/company-users :: companyUsersList";
    }

    /**
     * Remove users from a company
     */
    @DeleteMapping("/users/remove")
    public String removeUsersFromCompany(
            @RequestParam Long companyId,
            @RequestParam List<Integer> selectedUsers,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            Model model) {
        // Update each user's company to null
        for (Integer userId : selectedUsers) {
            userService.findById(userId).ifPresent(user -> {
                user.setCompany(null);
                userService.saveUser(user);
            });
        }

        // Get the company
        Company company = companyService.getCompanyById(companyId)
                .orElseThrow(() -> new IllegalArgumentException("Company not found"));

        // Get paginated users with this company name
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<User> userPage = userService.getUsersWithFilters(
                null, // fullName
                null, // email
                company.getCompanyName(), // company
                null, // role
                null, // enabled
                null, // accountLocked
                pageable);

        // Convert to DTOs
        Page<UserListDto> userDtoPage = userPage.map(userService::toUserListDto);

        model.addAttribute("users", userDtoPage);
        model.addAttribute("companyId", company.getId());
        model.addAttribute("companyName", company.getCompanyName());
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", userDtoPage.getTotalPages());

        return "company/fragments/company-users :: companyUsersList";
    }
}
