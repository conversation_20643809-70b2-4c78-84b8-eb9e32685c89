package ag.fuel.jobify.security.service;

import ag.fuel.jobify.security.entity.VerificationToken;
import ag.fuel.jobify.security.repository.VerificationTokenRepository;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.repository.UserRepository;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.time.LocalDateTime;
import java.util.Locale;
import java.util.Optional;

/**
 * Service for managing user account verification.
 */
@Service
@RequiredArgsConstructor
public class VerificationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(VerificationService.class);

    private final VerificationTokenRepository tokenRepository;
    private final UserRepository userRepository;
    private final JavaMailSender mailSender;
    private final MessageSource messageSource;
    private final TemplateEngine templateEngine;
    private final SecureTokenGenerator secureTokenGenerator;

    @Value("${spring.mail.username}")
    private String fromEmail;

    @Value("${application.base-url:http://localhost:8080}")
    private String baseUrl;
    
    /**
     * Creates a verification token for a user and sends a verification email.
     *
     * @param user The user to create a token for
     * @param locale The locale for email internationalization
     * @return The created verification token
     */
    @Transactional
    public VerificationToken createVerificationToken(User user, Locale locale) {
        // Create a new token with optimized SecureRandom
        VerificationToken token = new VerificationToken(user);
        // Override the default token with our secure token generator
        token.setToken(secureTokenGenerator.generateSecureToken());

        // Save the token
        VerificationToken savedToken = tokenRepository.save(token);

        // Send verification email
        sendVerificationEmail(user, token.getToken(), locale);

        return savedToken;
    }

    /**
     * Creates a verification token for a user and sends a verification email with default locale.
     *
     * @param user The user to create a token for
     * @return The created verification token
     */
    @Transactional
    public VerificationToken createVerificationToken(User user) {
        return createVerificationToken(user, Locale.getDefault());
    }
    
    /**
     * Verifies a user account using a verification token.
     * 
     * @param token The token string to verify
     * @return true if verification was successful, false otherwise
     */
    @Transactional
    public boolean verifyUser(String token) {
        Optional<VerificationToken> tokenOpt = tokenRepository.findByToken(token);
        
        if (!tokenOpt.isPresent()) {
            LOGGER.warn("Verification token not found: {}", token);
            return false;
        }
        
        VerificationToken verificationToken = tokenOpt.get();
        
        // Check if token is expired
        if (verificationToken.isExpired()) {
            LOGGER.warn("Verification token expired: {}", token);
            return false;
        }
        
        // Check if token is already used
        if (verificationToken.getVerifiedAt() != null) {
            LOGGER.warn("Verification token already used: {}", token);
            return false;
        }
        
        // Get the user
        User user = verificationToken.getUser();
        
        // Enable the user
        user.setEnabled(true);
        userRepository.save(user);
        
        // Mark token as used
        verificationToken.setVerifiedAt(LocalDateTime.now());
        tokenRepository.save(verificationToken);
        
        LOGGER.info("User verified successfully: {}", user.getEmail());
        
        return true;
    }
    
    /**
     * Sends a verification email to a user.
     *
     * @param user The user to send the email to
     * @param token The verification token
     * @param locale The locale for email internationalization
     */
    private void sendVerificationEmail(User user, String token, Locale locale) {
        try {
            String verificationUrl = baseUrl + "/auth/verify?token=" + token;

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setFrom(fromEmail);
            helper.setTo(user.getEmail());

            // Get localized subject
            String subject = messageSource.getMessage("email.verification.subject", null, "Account Verification", locale);
            helper.setSubject(subject);

            // Prepare the Thymeleaf context with locale for internationalization
            Context context = new Context(locale);
            context.setVariable("verificationUrl", verificationUrl);
            context.setVariable("userName", user.getFullName());

            // Process the HTML template
            String htmlContent = templateEngine.process("email/verification-email", context);

            helper.setText(htmlContent, true);

            mailSender.send(message);

            LOGGER.info("Verification email sent to: {}", user.getEmail());
        } catch (MessagingException e) {
            LOGGER.error("Failed to send verification email to {}: {}", user.getEmail(), e.getMessage());
        }
    }
    
    /**
     * Resends a verification email to a user.
     *
     * @param email The email of the user to resend the verification to
     * @param locale The locale for email internationalization
     * @return true if the email was sent, false otherwise
     */
    @Transactional
    public boolean resendVerificationEmail(String email, Locale locale) {
        Optional<User> userOpt = userRepository.findByEmail(email);

        if (!userOpt.isPresent()) {
            LOGGER.warn("User not found for email: {}", email);
            return false;
        }

        User user = userOpt.get();

        // Check if user is already verified
        if (user.isEnabled()) {
            LOGGER.warn("User already verified: {}", email);
            return false;
        }

        // Create a new token
        createVerificationToken(user, locale);

        return true;
    }

    /**
     * Resends a verification email to a user with default locale.
     *
     * @param email The email of the user to resend the verification to
     * @return true if the email was sent, false otherwise
     */
    @Transactional
    public boolean resendVerificationEmail(String email) {
        return resendVerificationEmail(email, Locale.getDefault());
    }
    
    /**
     * Scheduled task to clean up expired tokens.
     */
    @Scheduled(cron = "0 0 0 * * ?") // Run at midnight every day
    @Transactional
    public void cleanupExpiredTokens() {
        LOGGER.info("Cleaning up expired verification tokens");
        tokenRepository.deleteAllExpiredTokens(LocalDateTime.now());
    }
}
