package ag.fuel.jobify.security.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class CaptchaService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CaptchaService.class);
    private static final String RECAPTCHA_VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";
    
    private final RestTemplate restTemplate;
    
    @Value("${google.recaptcha.secret-key:}")
    private String recaptchaSecretKey;
    
    @Value("${google.recaptcha.threshold:0.5}")
    private float recaptchaThreshold;
    
    @Value("${google.recaptcha.enabled:false}")
    private boolean recaptchaEnabled;
    
    public CaptchaService(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
    }
    
    /**
     * Verifies the reCAPTCHA response
     * @param recaptchaResponse the g-recaptcha-response from the client
     * @return true if verification is successful, false otherwise
     */
    public boolean verifyCaptcha(String recaptchaResponse) {
        // If reCAPTCHA is not enabled, always return true
        if (!recaptchaEnabled) {
            return true;
        }
        
        // If no response or secret key, verification fails
        if (!StringUtils.hasLength(recaptchaResponse) || !StringUtils.hasLength(recaptchaSecretKey)) {
            return false;
        }
        
        try {
            // Create request parameters
            Map<String, String> params = new HashMap<>();
            params.put("secret", recaptchaSecretKey);
            params.put("response", recaptchaResponse);
            
            // Make verification request
            ResponseEntity<Map> response = restTemplate.postForEntity(
                    RECAPTCHA_VERIFY_URL + "?secret={secret}&response={response}",
                    null, Map.class, params);
            
            // Parse response
            Map<String, Object> body = response.getBody();
            if (body == null) {
                return false;
            }
            
            // Check if verification was successful
            boolean success = (Boolean) body.get("success");
            if (!success) {
                LOGGER.warn("reCAPTCHA verification failed: {}", body.get("error-codes"));
                return false;
            }
            
            // Check score for v3 reCAPTCHA
            if (body.containsKey("score")) {
                float score = ((Number) body.get("score")).floatValue();
                if (score < recaptchaThreshold) {
                    LOGGER.warn("reCAPTCHA score too low: {}", score);
                    return false;
                }
            }
            
            return true;
        } catch (RestClientException e) {
            LOGGER.error("Error verifying reCAPTCHA: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Checks if reCAPTCHA is enabled
     * @return true if reCAPTCHA is enabled, false otherwise
     */
    public boolean isEnabled() {
        return recaptchaEnabled;
    }
}
