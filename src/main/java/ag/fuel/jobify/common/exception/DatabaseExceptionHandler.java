package ag.fuel.jobify.common.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.dao.RecoverableDataAccessException;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.ModelAndView;

import java.net.SocketException;
import java.sql.SQLException;
import java.sql.SQLTimeoutException;

/**
 * Exception handler for database connection issues.
 * This handler catches specific database connection exceptions and redirects to a custom error page.
 */
@ControllerAdvice
public class DatabaseExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseExceptionHandler.class);

    /**
     * Handles general database connection exceptions.
     * 
     * @param ex The exception that was thrown
     * @return A ModelAndView that redirects to the database connection error page
     */
    @ExceptionHandler({
            SQLException.class,
            DataAccessResourceFailureException.class,
            CannotGetJdbcConnectionException.class
    })
    public ModelAndView handleDatabaseConnectionException(Exception ex) {
        logger.error("Database connection error: ", ex);

        // Log the exception details for debugging
        logger.error("Exception class: {}", ex.getClass().getName());
        logger.error("Exception message: {}", ex.getMessage());

        // Create a ModelAndView that redirects to the database connection error page
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/errors/database-connection-error");

        return modelAndView;
    }

    /**
     * Handles socket and timeout related database exceptions.
     * These are often recoverable with a retry mechanism.
     * 
     * @param ex The exception that was thrown
     * @return A ModelAndView that redirects to the database connection error page
     */
    @ExceptionHandler({
            SocketException.class,
            SQLTimeoutException.class,
            QueryTimeoutException.class,
            RecoverableDataAccessException.class
    })
    public ModelAndView handleSocketAndTimeoutException(Exception ex) {
        logger.error("Database socket or timeout error: ", ex);

        // Log specific details for socket closed errors
        if (ex.getMessage() != null && ex.getMessage().contains("Socket closed")) {
            logger.error("Socket closed error detected. This may be due to network issues or connection pool settings.");
            logger.error("Check connection pool settings and network stability.");
        }

        // Log the exception details for debugging
        logger.error("Exception class: {}", ex.getClass().getName());
        logger.error("Exception message: {}", ex.getMessage());

        // Create a ModelAndView that redirects to the database connection error page
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/errors/database-connection-error");
        modelAndView.addObject("isRecoverable", true);
        modelAndView.addObject("errorMessage", "A database connection issue occurred. The system will automatically retry your request.");

        return modelAndView;
    }
}
