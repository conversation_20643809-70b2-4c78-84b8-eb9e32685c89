package ag.fuel.jobify.communication.service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.util.Locale;

@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements EmailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(EmailServiceImpl.class);

    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine; // For HTML email templates
    private final MessageSource messageSource; // For internationalization

    @Value("${spring.mail.username}")
    private String fromEmail;

    @Override
    public void sendPasswordResetEmail(String to, String subject, String resetUrl, Locale locale) throws MessagingException {
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, "utf-8");

        // Create a context with the correct locale
        Context context = createContext(locale);
        context.setVariable("resetUrl", resetUrl);
        // You can add more variables to the context if your template needs them
        // e.g., context.setVariable("username", user.getFullName());

        // Process the HTML template
        String htmlContent = templateEngine.process("email/password-reset-email", context);

        helper.setText(htmlContent, true); // true = is HTML
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setFrom(fromEmail);

        mailSender.send(mimeMessage);
    }

    /**
     * Creates a Thymeleaf context with the correct locale
     * 
     * @param locale The locale to use for the context
     * @return A Thymeleaf context with the correct locale
     */
    private Context createContext(Locale locale) {
        // If the language is Portuguese but no country is specified, use Brazilian Portuguese
        if (locale.getLanguage().equals("pt") && (locale.getCountry() == null || locale.getCountry().isEmpty())) {
            locale = new Locale("pt", "BR");
        }

        // Create a new context with the specified locale
        Context context = new Context(locale);

        // Force the locale to be set explicitly on the context
        context.setLocale(locale);

        return context;
    }

    @Override
    public void sendUserActivationEmail(String to, String subject, String activationUrl, Locale locale) throws MessagingException {
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, "utf-8");

        // Create a context with the correct locale
        Context context = createContext(locale);
        context.setVariable("activationUrl", activationUrl);

        // Process the HTML template
        String htmlContent = templateEngine.process("email/user-activation-email", context);

        helper.setText(htmlContent, true); // true = is HTML
        helper.setTo(to);
        helper.setSubject(subject);
        helper.setFrom(fromEmail);

        mailSender.send(mimeMessage);
    }
}
