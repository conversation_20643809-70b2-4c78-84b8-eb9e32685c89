package ag.fuel.jobify.user.dto;

import ag.fuel.jobify.auth.entity.ERole;

import java.util.Date;
import java.util.Set;

/**
 * DTO for user listing in admin management page
 */
public record UserListDto(
    Integer id,
    String fullName,
    String email,
    String company,
    String phone,
    String country,
    String city,
    String position,
    boolean enabled,
    boolean accountLocked,
    Date createdAt,
    Date updatedAt,
    Set<ERole> roles,
    String photo
) {}
