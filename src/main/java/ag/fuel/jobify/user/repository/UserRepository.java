package ag.fuel.jobify.user.repository;

import ag.fuel.jobify.user.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Integer> {

    Optional<User> findByEmail(String email);

    Boolean existsByEmail(String email);

    Page<User> findAll(Pageable pageable);

    @Query("UPDATE User u SET u.failedAttempt = ?1 WHERE u.email = ?2")
    @Modifying
    void updateFailedAttempts(int failAttempts, String email);

    @Query("UPDATE User u SET u.accountLocked = ?1, u.lockTime = ?2 WHERE u.email = ?3")
    @Modifying
    void updateAccountLockStatus(boolean locked, Date lockTime, String email);

    @Query("UPDATE User u SET u.failedAttempt = 0, u.accountLocked = false, u.lockTime = null WHERE u.email = ?1")
    @Modifying
    void resetFailedAttempts(String email);

    @Query("SELECT u FROM User u WHERE u.enabled = false")
    Page<User> findUnverifiedUsers(Pageable pageable);

    @Query("SELECT u FROM User u WHERE u.accountLocked = true")
    Page<User> findLockedUsers(Pageable pageable);

    @Query("SELECT COUNT(u) FROM User u WHERE u.enabled = true")
    long countEnabledUsers();

    @Query("SELECT COUNT(u) FROM User u WHERE u.enabled = false")
    long countDisabledUsers();

    @Query("SELECT COUNT(u) FROM User u WHERE u.accountLocked = true")
    long countLockedUsers();

    @Query("SELECT COUNT(u) FROM User u WHERE u.company IS NULL OR u.company = ''")
    long countUsersWithoutCompany();

    /**
     * Find users with filtering support for admin management
     */
    @Query("SELECT DISTINCT u FROM User u LEFT JOIN u.roles r WHERE " +
           "(:fullName IS NULL OR LOWER(u.fullName) LIKE LOWER(CONCAT('%', :fullName, '%'))) AND " +
           "(:email IS NULL OR LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%'))) AND " +
           "(:company IS NULL OR " +
           " (:company = 'NO_COMPANY' AND (u.company IS NULL OR u.company = '')) OR " +
           " (:company <> 'NO_COMPANY' AND LOWER(u.company) LIKE LOWER(CONCAT('%', :company, '%')))) AND " +
           "(:role IS NULL OR r.name = :role) AND " +
           "(:enabled IS NULL OR u.enabled = :enabled) AND " +
           "(:accountLocked IS NULL OR u.accountLocked = :accountLocked)")
    Page<User> findUsersWithFilters(
            @Param("fullName") String fullName,
            @Param("email") String email,
            @Param("company") String company,
            @Param("role") ag.fuel.jobify.auth.entity.ERole role,
            @Param("enabled") Boolean enabled,
            @Param("accountLocked") Boolean accountLocked,
            Pageable pageable);

    /**
     * Get all distinct countries for filter dropdown
     */
    @Query("SELECT DISTINCT u.country FROM User u WHERE u.country IS NOT NULL ORDER BY u.country")
    java.util.List<String> findDistinctCountries();

    /**
     * Get all distinct companies for filter dropdown
     */
    @Query("SELECT DISTINCT u.company FROM User u WHERE u.company IS NOT NULL ORDER BY u.company")
    java.util.List<String> findDistinctCompanies();

    /**
     * Get all distinct roles for filter dropdown
     */
    @Query("SELECT DISTINCT r.name FROM User u JOIN u.roles r ORDER BY r.name")
    java.util.List<ag.fuel.jobify.auth.entity.ERole> findDistinctRoles();

    /**
     * Find users that don't belong to a specific company with filtering support
     */
    @Query("SELECT DISTINCT u FROM User u LEFT JOIN u.roles r WHERE " +
           "(u.company IS NULL OR u.company <> :companyName) AND " +
           "(:fullName IS NULL OR LOWER(u.fullName) LIKE LOWER(CONCAT('%', :fullName, '%'))) AND " +
           "(:email IS NULL OR LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%'))) AND " +
           "(:role IS NULL OR r.name = :role) AND " +
           "(:enabled IS NULL OR u.enabled = :enabled) AND " +
           "(:accountLocked IS NULL OR u.accountLocked = :accountLocked)")
    Page<User> findUsersNotInCompany(
            @Param("companyName") String companyName,
            @Param("fullName") String fullName,
            @Param("email") String email,
            @Param("role") ag.fuel.jobify.auth.entity.ERole role,
            @Param("enabled") Boolean enabled,
            @Param("accountLocked") Boolean accountLocked,
            Pageable pageable);
}
