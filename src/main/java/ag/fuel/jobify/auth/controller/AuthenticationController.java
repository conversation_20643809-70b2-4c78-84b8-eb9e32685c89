package ag.fuel.jobify.auth.controller;

import ag.fuel.jobify.auth.dto.VerifyUserDto;
import ag.fuel.jobify.auth.dto.LoginUserDto;
import ag.fuel.jobify.auth.dto.RegisterUserDto;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.repository.UserRepository;
import ag.fuel.jobify.auth.dto.LoginResponse;
import ag.fuel.jobify.auth.dto.SignupResponse;
import ag.fuel.jobify.common.dto.MessageResponse;
import ag.fuel.jobify.security.service.AuthenticationService;
import ag.fuel.jobify.security.service.CaptchaService;
import ag.fuel.jobify.security.service.JwtService;
import ag.fuel.jobify.security.service.LoginAttemptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.LockedException;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RequestMapping("/auth")
@RestController
@RequiredArgsConstructor
@Tag(name = "Authentication", description = "Authentication management APIs")
public class AuthenticationController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthenticationController.class);

    private final JwtService jwtService;
    private final AuthenticationService authenticationService;
    private final CaptchaService captchaService;
    private final LoginAttemptService loginAttemptService;
    private final HttpServletRequest request;
    private final UserRepository userRepository;

    // Record class for refresh token request
    record RefreshTokenRequest(String refreshToken) {}

    // Record class for refresh token response
    record RefreshTokenResponse(String accessToken, String refreshToken, long expiresIn) {}

    @Operation(summary = "Register new user", description = "Register a new user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User registered successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "Email already in use")
    })
    @PostMapping("/signup")
    public ResponseEntity<?> register(
            @Parameter(description = "User registration data") @Valid @RequestBody RegisterUserDto registerUserDto) {
        User registeredUser = authenticationService.signup(registerUserDto);

        if (registeredUser == null) {
            return ResponseEntity.ok(new MessageResponse("Error: Email is already in use!"));
        }

        List<String> authorities = registeredUser.getAuthorities().stream()
                .map(item -> item.getAuthority())
                .collect(Collectors.toList());

        // Create SignupResponse using record constructor
        SignupResponse signupResponse = new SignupResponse(
            registeredUser.getId(),
            registeredUser.getFullName(),
            registeredUser.getEmail(),
            null, // Don't expose password hash
            registeredUser.getCreatedAt(),
            registeredUser.getUpdatedAt(),
            registeredUser.isEnabled(),
            authorities,
            !registeredUser.isAccountLocked(),
            true, // Default value
            true  // Default value
        );

        return ResponseEntity.ok(signupResponse);
    }

    @Operation(summary = "Authenticate user", description = "Authenticate a user with email and password")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully authenticated"),
        @ApiResponse(responseCode = "401", description = "Invalid credentials"),
        @ApiResponse(responseCode = "403", description = "Account locked"),
        @ApiResponse(responseCode = "429", description = "Too many requests")
    })
    @PostMapping("/login")
    public ResponseEntity<?> authenticate(
            @Parameter(description = "User login credentials") @Valid @RequestBody LoginUserDto loginUserDto,
            @Parameter(description = "reCAPTCHA response token") @RequestParam(name = "g-recaptcha-response", required = false) String recaptchaResponse) {
        try {
            // Check if CAPTCHA is required and verify it
            if (loginAttemptService.isBlocked() && (recaptchaResponse == null || !captchaService.verifyCaptcha(recaptchaResponse))) {
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                        .body(new MessageResponse("Too many failed login attempts. Please complete the CAPTCHA."));
            }

            User authenticatedUser = authenticationService.authenticate(loginUserDto);

            String jwtToken = jwtService.generateToken(authenticatedUser);

            List<String> authorities = authenticatedUser.getAuthorities().stream()
                    .map(item -> item.getAuthority())
                    .collect(Collectors.toList());

            LoginResponse loginResponse = new LoginResponse(
                    jwtToken,
                    jwtService.getExpirationTime(),
                    authorities
            );

            return ResponseEntity.ok(loginResponse);
        } catch (BadCredentialsException e) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(new MessageResponse("Invalid username or password"));
        } catch (LockedException e) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(new MessageResponse(e.getMessage()));
        } catch (Exception e) {
            // Let the GlobalExceptionHandler handle other authentication exceptions
            // including DisabledException with internationalized messages
            throw e;
        }
    }

    @PostMapping("/verify")
    public ResponseEntity<?> verifyUser(@RequestBody VerifyUserDto verifyUserDto) {
        try {
            authenticationService.verifyUser(verifyUserDto);
            return ResponseEntity.ok("Account verified successfully");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/verify")
    public ResponseEntity<?> verifyUserByToken(@RequestParam("token") String token) {
        try {
            VerifyUserDto verifyUserDto = new VerifyUserDto("", token);
            authenticationService.verifyUser(verifyUserDto);
            return ResponseEntity.ok("Account verified successfully. You can now login.");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping("/resend-verification")
    public ResponseEntity<?> resendVerificationEmail(@RequestParam("email") String email) {
        boolean sent = authenticationService.resendVerificationEmail(email);
        if (sent) {
            return ResponseEntity.ok("Verification email sent successfully");
        } else {
            return ResponseEntity.badRequest().body("Failed to send verification email");
        }
    }

    @PostMapping("/refresh-token")
    public ResponseEntity<?> refreshToken(@RequestBody RefreshTokenRequest refreshRequest) {
        try {
            String refreshToken = refreshRequest.refreshToken();

            // Validate the refresh token
            if (refreshToken == null || refreshToken.isEmpty()) {
                return ResponseEntity.badRequest().body(new MessageResponse("Refresh token is required"));
            }

            // Check if it's a valid refresh token
            if (!jwtService.isRefreshToken(refreshToken)) {
                LOGGER.warn("Invalid refresh token type attempted");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new MessageResponse("Invalid refresh token"));
            }

            // Extract username from token
            String username = jwtService.extractUsername(refreshToken);
            if (username == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new MessageResponse("Invalid refresh token"));
            }

            // Get user details
            Optional<User> userOptional = userRepository.findByEmail(username);
            if (userOptional.isEmpty()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new MessageResponse("User not found"));
            }
            User user = userOptional.get();

            // Validate the token
            if (!jwtService.isTokenValid(refreshToken, user)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(new MessageResponse("Invalid refresh token"));
            }

            // Generate new tokens
            String newAccessToken = jwtService.generateToken(user);
            String newRefreshToken = jwtService.generateRefreshToken(user);

            LOGGER.info("Tokens refreshed for user: {}", username);

            // Return the new tokens
            return ResponseEntity.ok(new RefreshTokenResponse(
                    newAccessToken,
                    newRefreshToken,
                    jwtService.getExpirationTime()
            ));

        } catch (Exception e) {
            LOGGER.error("Error refreshing token: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new MessageResponse("Error refreshing token"));
        }
    }
}
