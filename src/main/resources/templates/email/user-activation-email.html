<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}">
<head>
    <meta charset="UTF-8">
    <title>[[#{email.user.activation.title}]]</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { width: 80%; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .button { display: inline-block; padding: 10px 20px; background-color: #7367f0; color: #ffffff !important; text-decoration: none; border-radius: 3px; cursor: pointer; }
        .footer { margin-top: 20px; font-size: 0.9em; color: #777; }
    </style>
</head>
<body>
    <div class="container">
        <h2>[[#{email.user.activation.title}]]</h2>
        <p>[[#{email.user.activation.p.01}]]</p>
        <p>[[#{email.user.activation.p.02}]]</p>
        <p>
            <a th:href="${activationUrl}" class="button">[[#{email.user.activation.button}]]</a>
        </p>
        <p>[[#{email.user.activation.p.03}]]</p>
        <p class="footer">
            [[#{email.user.activation.footer.01}]]<br/>
            [[#{email.user.activation.footer.02}]]
        </p>
    </div>
</body>
</html>
