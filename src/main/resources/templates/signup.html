<!doctype html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org" th:replace="~{index::layout(~{::section})}">
<head>
  <meta charset="UTF-8"/>
  <title th:text="#{page.signup.title}">Jobify - Sign Up</title>
</head>
<body>
<section class="center">
  <h1 th:text="#{page.signup.heading}">Sign Up for Jobify</h1>

  <div class="card mx-auto" style="max-width: 400px;">
    <div class="card-body">
      <form id="signupForm">
        <div class="mb-3">
          <label for="fullName" class="form-label" th:text="#{user.field.fullName}">Full Name</label>
          <input type="text" class="form-control" id="fullName" name="fullName" required>
        </div>

        <div class="mb-3">
          <label for="email" class="form-label" th:text="#{user.field.email}">Email</label>
          <input type="email" class="form-control" id="email" name="email" required>
        </div>

        <div class="mb-3">
          <label for="password" class="form-label" th:text="#{user.field.password}">Password</label>
          <div class="input-group">
            <input type="password"
                   class="form-control"
                   id="password"
                   name="password"
                   minlength="8"
                   pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\d@#$!%*?&.;:<>]{8,}$"
                   th:title="#{validation.password.pattern}"
                   oninput="validatePasswordField('password')"
                   onblur="validatePasswordField('password')"
                   required>
            <button class="btn btn-outline-secondary"
                    type="button"
                    id="togglePassword"
                    onclick="togglePasswordVisibility('password', 'togglePassword')"
                    title="Show/Hide Password">
              <i class="ti ti-eye" id="togglePasswordIcon"></i>
            </button>
          </div>
          <div class="invalid-feedback" id="password-feedback"></div>
          <div class="form-text">
            <small class="text-muted">
              <div id="password-requirements" class="password-requirements mt-2">
                <div class="requirement" id="req-length">
                  <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.length}">At least 8 characters</span>
                </div>
                <div class="requirement" id="req-lowercase">
                  <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.lowercase}">One lowercase letter</span>
                </div>
                <div class="requirement" id="req-uppercase">
                  <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.uppercase}">One uppercase letter</span>
                </div>
                <div class="requirement" id="req-number">
                  <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.number}">One number</span>
                </div>
                <div class="requirement" id="req-special">
                  <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.special}">One special character</span>
                </div>
              </div>
            </small>
          </div>
        </div>

        <div class="mb-3">
          <label for="confirmPassword" class="form-label" th:text="#{page.signup.confirmPassword}">Confirm Password</label>
          <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
        </div>

        <button type="submit" class="btn btn-primary w-100" th:text="#{page.signup.button}">Sign Up</button>
      </form>

      <div class="text-center mt-3">
        <p><span th:text="#{page.signup.hasAccount}">Already have an account?</span> <a th:href="@{/login}" th:text="#{page.signup.loginLink}">Login here</a></p>
      </div>
    </div>
  </div>
</section>

<style>
.password-requirements {
    font-size: 0.875rem;
}

.password-requirements .requirement {
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.password-requirements .requirement.valid i {
    color: #28a745 !important;
}

.password-requirements .requirement.valid i:before {
    content: "\ea5b"; /* ti-check icon */
}

.password-requirements .requirement.invalid i {
    color: #dc3545 !important;
}

.password-requirements .requirement.invalid i:before {
    content: "\eb55"; /* ti-x icon */
}
</style>

<script>
// Password visibility toggle function
function togglePasswordVisibility(passwordFieldId, toggleButtonId) {
    const passwordField = document.getElementById(passwordFieldId);
    const toggleIcon = document.querySelector(`#${toggleButtonId} i`);

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.className = 'ti ti-eye-off';
    } else {
        passwordField.type = 'password';
        toggleIcon.className = 'ti ti-eye';
    }
}

// Password validation function with real-time feedback
function validatePasswordField(fieldId) {
    const field = document.getElementById(fieldId);
    const value = field.value;
    const feedbackElement = document.getElementById(fieldId + '-feedback');

    // Requirements elements
    const reqLength = document.getElementById('req-length');
    const reqLowercase = document.getElementById('req-lowercase');
    const reqUppercase = document.getElementById('req-uppercase');
    const reqNumber = document.getElementById('req-number');
    const reqSpecial = document.getElementById('req-special');

    if (!value) {
        // Reset all requirements to invalid state when field is empty
        resetRequirement(reqLength);
        resetRequirement(reqLowercase);
        resetRequirement(reqUppercase);
        resetRequirement(reqNumber);
        resetRequirement(reqSpecial);

        field.classList.remove('is-valid', 'is-invalid');
        if (feedbackElement) feedbackElement.innerHTML = '';
        return;
    }

    // Check each requirement
    const hasLength = value.length >= 8;
    const hasLowercase = /[a-z]/.test(value);
    const hasUppercase = /[A-Z]/.test(value);
    const hasNumber = /\d/.test(value);
    const hasSpecial = /[@#$!%*?&.;:<>]/.test(value);

    // Update requirement indicators
    updateRequirement(reqLength, hasLength);
    updateRequirement(reqLowercase, hasLowercase);
    updateRequirement(reqUppercase, hasUppercase);
    updateRequirement(reqNumber, hasNumber);
    updateRequirement(reqSpecial, hasSpecial);

    // Overall validation
    const isValid = hasLength && hasLowercase && hasUppercase && hasNumber && hasSpecial;

    if (isValid) {
        field.classList.add('is-valid');
        field.classList.remove('is-invalid');
        field.setCustomValidity('');
        if (feedbackElement) feedbackElement.innerHTML = '';
    } else {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        field.setCustomValidity('Password does not meet requirements');

        // Show specific error message
        const errors = [];
        if (!hasLength) errors.push('At least 8 characters');
        if (!hasLowercase) errors.push('One lowercase letter');
        if (!hasUppercase) errors.push('One uppercase letter');
        if (!hasNumber) errors.push('One number');
        if (!hasSpecial) errors.push('One special character');

        if (feedbackElement) {
            feedbackElement.innerHTML = 'Password must contain: ' + errors.join(', ');
        }
    }
}

function updateRequirement(element, isValid) {
    if (!element) return;

    const icon = element.querySelector('i');
    if (isValid) {
        element.classList.add('valid');
        element.classList.remove('invalid');
        if (icon) {
            icon.className = 'ti ti-check text-success';
        }
    } else {
        element.classList.add('invalid');
        element.classList.remove('valid');
        if (icon) {
            icon.className = 'ti ti-x text-danger';
        }
    }
}

function resetRequirement(element) {
    if (!element) return;

    element.classList.remove('valid', 'invalid');
    const icon = element.querySelector('i');
    if (icon) {
        icon.className = 'ti ti-x text-danger';
    }
}

// Initialize validation when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize password validation for existing fields
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            validatePasswordField('password');
        });
        passwordField.addEventListener('blur', function() {
            validatePasswordField('password');
        });
    }
});

document.getElementById('signupForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = {
        fullName: document.getElementById('fullName').value,
        email: document.getElementById('email').value,
        password: document.getElementById('password').value
    };

    const confirmPassword = document.getElementById('confirmPassword').value;

    if (formData.password !== confirmPassword) {
        alert('Passwords do not match!');
        return;
    }

    // Validate password before submission
    const passwordField = document.getElementById('password');
    if (passwordField.classList.contains('is-invalid')) {
        alert('Please ensure your password meets all requirements');
        return;
    }

    fetch('/auth/signup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            alert(data.message);
            if (data.message.includes('successfully')) {
                window.location.href = '/login';
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred during signup');
    });
});
</script>
</body>
</html>
