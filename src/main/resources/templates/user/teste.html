<!doctype html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>

    <title th:text="#{page.test.title}">Test Page</title>
</head>
<body>

<div layout:fragment="content">

    <h5 th:text="#{page.test.heading}">Test Page</h5>

    <small class="text-muted" th:text="${currentUser.getEmail()}"></small>
    <br>
</div>


<th:block layout:fragment="optionalPageJS">
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.4.1.slim.min.js"></script>
</th:block>

</body>
</html>