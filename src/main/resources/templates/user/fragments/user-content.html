<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org">

<body>

<div th:fragment="userContent"
     id="userContent"
     hx-trigger="refresh from:body"
     hx-get="/admin/users/refresh"
     hx-swap="outerHTML"
     hx-indicator="#refreshSpinner">
    <!-- User Statistics Cards -->
    <div class="row mb-6">
        <div class="col-xl col-lg-4 col-md-6 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{user.stats.total}">Total Users</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${totalUsersCount}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{user.stats.total.description}">All registered users</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-primary">
                                <i class="ti ti-users ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-4 col-md-6 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{user.stats.active}">Active Users</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${activeUsersCount}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{user.stats.active.description}">Enabled accounts</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-success">
                                <i class="ti ti-user-check ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-4 col-md-6 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{user.stats.inactive}">Inactive Users</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${inactiveUsersCount}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{user.stats.inactive.description}">Disabled accounts</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-warning">
                                <i class="ti ti-user-off ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-4 col-md-6 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{user.stats.locked}">Locked Users</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${lockedUsersCount}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{user.stats.locked.description}">Locked accounts</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-danger">
                                <i class="ti ti-lock ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-4 col-md-6 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{user.stats.without.company}">Users Without Company</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${usersWithoutCompanyCount}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{user.stats.without.company.description}">Users without assigned company</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-info">
                                <i class="ti ti-building-off ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User List Card -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" th:text="#{user.list.title}">Users</h5>
                <!-- Loading Spinner -->
                <div id="refreshSpinner" class="htmx-indicator">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-datatable table-responsive">
            <div id="userList" th:replace="~{user/fragments/user-list :: userList}"></div>
        </div>
    </div>
</div>

</body>
</html>
