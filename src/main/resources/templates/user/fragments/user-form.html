<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org">

<body>

<div th:fragment="userForm">
    <!-- Update offcanvas title using HTMX -->
    <div hx-swap-oob="innerHTML:#userFormOffcanvasLabel" th:text="${isEdit} ? #{user.edit.title} : #{user.create.title}"></div>

    <!-- Alert Box for General Errors -->
    <div id="userFormAlert" class="alert alert-dismissible fade" role="alert" style="display: none;">
        <div class="d-flex align-items-center">
            <i id="userFormAlertIcon" class="ti ti-alert-circle me-2"></i>
            <div>
                <strong id="userFormAlertTitle" th:text="#{alert.error.title}">Error</strong>
                <div id="userFormAlertMessage" th:text="#{alert.error.unknown}">An unknown error occurred</div>
            </div>
        </div>
        <button type="button" class="btn-close" onclick="hideUserFormAlert()" th:attr="aria-label=#{common.close}"></button>
    </div>

    <form th:if="${!isEdit}"
          id="createUserForm"
          th:object="${createUserDto}"
          hx-post="/admin/users/create"
          hx-swap="innerHTML"
          hx-target="#response"
          hx-trigger="bs-send"
          class="needs-validation user-form"
          novalidate>

        <div>
            <!-- Full Name -->
            <div class="mb-3">
                <label for="fullName" class="form-label"><span th:text="#{user.field.fullName}">Full Name</span> *</label>
                <input type="text"
                       class="form-control"
                       id="fullName"
                       name="fullName"
                       th:field="*{fullName}"
                       minlength="2"
                       maxlength="100"
                       th:title="#{user.field.fullName.required}"
                       required>
                <div class="invalid-feedback" th:text="#{user.field.fullName.required}"></div>
            </div>

            <!-- Email -->
            <div class="mb-3">
                <label for="email" class="form-label"><span th:text="#{user.field.email}">Email</span> *</label>
                <input type="email"
                       class="form-control"
                       id="email"
                       name="email"
                       th:field="*{email}"
                       pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}"
                       th:title="#{user.field.email.invalid}"
                       required>
                <div class="invalid-feedback" th:text="#{user.field.email.invalid}"></div>
            </div>

            <!-- Password -->
            <div class="mb-3">
                <label for="password" class="form-label"><span th:text="#{user.field.password}">Password</span> *</label>
                <div class="input-group">
                    <input type="password"
                           class="form-control"
                           id="password"
                           name="password"
                           th:field="*{password}"
                           minlength="8"
                           pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\d@#$!%*?&.;:<>]{8,}$"
                           th:title="#{validation.password.pattern}"
                           oninput="validatePasswordField('password')"
                           onblur="validatePasswordField('password')"
                           required>
                    <button class="btn btn-outline-secondary"
                            type="button"
                            id="togglePassword"
                            onclick="togglePasswordVisibility('password', 'togglePassword')"
                            title="Show/Hide Password">
                        <i class="ti ti-eye" id="togglePasswordIcon"></i>
                    </button>
                </div>
                <div class="invalid-feedback" id="password-feedback"></div>
                <div class="form-text">
                    <small class="text-muted">
                        <div id="password-requirements" class="password-requirements mt-2">
                            <div class="requirement" id="req-length">
                                <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.length}">At least 8 characters</span>
                            </div>
                            <div class="requirement" id="req-lowercase">
                                <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.lowercase}">One lowercase letter</span>
                            </div>
                            <div class="requirement" id="req-uppercase">
                                <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.uppercase}">One uppercase letter</span>
                            </div>
                            <div class="requirement" id="req-number">
                                <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.number}">One number</span>
                            </div>
                            <div class="requirement" id="req-special">
                                <i class="ti ti-x text-danger"></i> <span th:text="#{validation.password.req.special}">One special character</span>
                            </div>
                        </div>
                    </small>
                </div>
            </div>

            <!-- Company -->
            <div class="mb-3">
                <label for="companyId" class="form-label"><span th:text="#{user.field.company}">Company</span> *</label>
                <select class="form-select"
                        id="companyId"
                        name="companyId"
                        th:field="*{companyId}"
                        required>
                    <option value="" th:text="#{user.form.select.company}">Select Company</option>
                    <option th:each="company : ${allCompanies}"
                            th:value="${company.id}"
                            th:text="${company.companyName}">Company</option>
                </select>
                <div class="invalid-feedback" th:text="#{user.field.company.required}"></div>
            </div>

            <!-- Phone -->
            <div class="mb-3">
                <label for="phone" class="form-label" th:text="#{user.field.phone}">Phone</label>
                <input type="text"
                       class="form-control"
                       id="phone"
                       name="phone"
                       th:field="*{phone}"
                       maxlength="20"
                       th:title="#{user.field.phone.maxlength}">
                <div class="invalid-feedback" th:text="#{user.field.phone.maxlength}"></div>
            </div>

            <!-- Country -->
            <div class="mb-3">
                <label for="country" class="form-label" th:text="#{user.field.country}">Country</label>
                <select class="form-select"
                        id="country"
                        name="country"
                        th:field="*{country}">
                    <option value="" th:text="#{user.form.select.country}">Select Country</option>
                    <option th:each="country : ${allCountries}"
                            th:value="${country.code}"
                            th:text="${country.name}">Country</option>
                </select>
                <div class="invalid-feedback"></div>
            </div>

            <!-- Language -->
            <div class="mb-3">
                <label for="language" class="form-label"><span th:text="#{user.field.language}">Language</span> *</label>
                <select class="form-select"
                        id="language"
                        name="language"
                        th:field="*{language}"
                        required>
                    <option value="" th:text="#{user.form.select.language}">Select Language</option>
                    <option th:each="language : ${allLanguages}"
                            th:value="${language.code}"
                            th:text="${language.name}">Language</option>
                </select>
                <div class="invalid-feedback" th:text="#{user.field.language.required}"></div>
            </div>

            <!-- City -->
            <div class="mb-3">
                <label for="city" class="form-label" th:text="#{user.field.city}">City</label>
                <input type="text"
                       class="form-control"
                       id="city"
                       name="city"
                       th:field="*{city}"
                       maxlength="100"
                       th:title="#{user.field.city.maxlength}">
                <div class="invalid-feedback" th:text="#{user.field.city.maxlength}"></div>
            </div>

            <!-- Work Format -->
            <div class="mb-3">
                <label for="workFormat" class="form-label"><span th:text="#{user.field.workFormat}">Work Format</span> *</label>
                <select class="form-select"
                        id="workFormat"
                        name="workFormat"
                        th:field="*{workFormat}"
                        required>
                    <option value="" th:text="#{user.form.select.workFormat}">Select Work Format</option>
                    <option th:each="workFormat : ${allWorkFormats}"
                            th:value="${workFormat.id}"
                            th:text="${workFormat.name}">Work Format</option>
                </select>
                <div class="invalid-feedback" th:text="#{user.field.workFormat.required}"></div>
            </div>

            <!-- Position -->
            <div class="mb-3">
                <label for="position" class="form-label" th:text="#{user.field.position}">Position</label>
                <input type="text"
                       class="form-control"
                       id="position"
                       name="position"
                       th:field="*{position}"
                       maxlength="100"
                       th:title="#{user.field.position.maxlength}">
                <div class="invalid-feedback" th:text="#{user.field.position.maxlength}"></div>
            </div>

            <!-- Joined Date -->
            <div class="mb-3">
                <label for="joinedDate" class="form-label" th:text="#{user.field.joinedDate}">Joined Date</label>
                <input type="date"
                       class="form-control"
                       id="joinedDate"
                       name="joinedDate"
                       th:field="*{joinedDate}">
                <div class="invalid-feedback"></div>
            </div>

            <!-- Status -->
            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input"
                           type="checkbox"
                           id="enabled"
                           name="enabled"
                           th:field="*{enabled}">
                    <label class="form-check-label" for="enabled" th:text="#{user.field.enabled}">
                        Account Enabled
                    </label>
                </div>
            </div>

            <!-- Roles -->
            <div class="mb-3">
                <label class="form-label">
                    <span th:text="#{user.field.roles}">Role</span>
                    <span th:if="!${isCurrentUserAdmin}"> *</span>
                </label>
                <div class="row" id="roles-container">
                    <div th:each="role : ${allRoles}" class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input role-radio"
                                   type="radio"
                                   th:id="'role_' + ${role.name}"
                                   name="roles"
                                   th:value="${role.name}"
                                   th:checked="${createUserDto.roles != null && createUserDto.roles == role.name}"
                                   th:required="!${isCurrentUserAdmin}"
                                   onchange="validateRoleSelection('create')">
                            <label class="form-check-label"
                                   th:for="'role_' + ${role.name}"
                                   th:text="${#strings.replace(role.name, 'ROLE_', '')}">
                                Role
                            </label>
                        </div>
                    </div>
                </div>
                <div class="invalid-feedback" id="rolesInvalidFeedback" th:text="#{user.validation.roles.required}" style="display: block;"></div>
            </div>
        </div>

        <div class="offcanvas-footer mt-4 pt-3 border-top">
            <button type="button"
                    class="btn btn-outline-secondary"
                    data-bs-dismiss="offcanvas"
                    th:text="#{user.button.cancel}">Cancel</button>
            <button type="submit"
                    class="btn btn-primary"
                    hx-indicator="#createSpinner">
                <span id="createSpinner" class="spinner-border spinner-border-sm htmx-indicator me-2" role="status"></span>
                <span th:text="#{user.button.create}">Create User</span>
            </button>
        </div>
    </form>

    <form th:if="${isEdit}"
          id="editUserForm"
          th:object="${editUserDto}"
          hx-post="/admin/users/update"
          hx-swap="innerHTML"
          hx-target="#response"
          hx-trigger="bs-send"
          class="needs-validation user-form"
          novalidate>

        <input type="hidden" name="id" th:field="*{id}">

        <div>
            <!-- Full Name -->
            <div class="mb-3">
                <label for="editFullName" class="form-label"><span th:text="#{user.field.fullName}">Full Name</span> *</label>
                <input type="text"
                       class="form-control"
                       id="editFullName"
                       name="fullName"
                       th:field="*{fullName}"
                       minlength="2"
                       maxlength="100"
                       th:title="#{user.field.fullName.required}"
                       required>
                <div class="invalid-feedback" th:text="#{user.field.fullName.required}"></div>
            </div>

            <!-- Email -->
            <div class="mb-3">
                <label for="editEmail" class="form-label"><span th:text="#{user.field.email}">Email</span> *</label>
                <input type="email"
                       class="form-control"
                       id="editEmail"
                       name="email"
                       th:field="*{email}"
                       pattern="[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}"
                       th:title="#{user.field.email.invalid}"
                       required>
                <div class="invalid-feedback" th:text="#{user.field.email.invalid}"></div>
            </div>

            <!-- Company -->
            <div class="mb-3">
                <label for="editCompanyId" class="form-label"><span th:text="#{user.field.company}">Company</span> *</label>
                <select class="form-select"
                        id="editCompanyId"
                        name="companyId"
                        th:field="*{companyId}"
                        required>
                    <option value="" th:text="#{user.form.select.company}">Select Company</option>
                    <option th:each="company : ${allCompanies}"
                            th:value="${company.id}"
                            th:text="${company.companyName}">Company</option>
                </select>
                <div class="invalid-feedback" th:text="#{user.field.company.required}"></div>
            </div>

            <!-- Phone -->
            <div class="mb-3">
                <label for="editPhone" class="form-label" th:text="#{user.field.phone}">Phone</label>
                <input type="text"
                       class="form-control"
                       id="editPhone"
                       name="phone"
                       th:field="*{phone}"
                       maxlength="20"
                       th:title="#{user.field.phone.maxlength}">
                <div class="invalid-feedback" th:text="#{user.field.phone.maxlength}"></div>
            </div>

            <!-- Country -->
            <div class="mb-3">
                <label for="editCountry" class="form-label" th:text="#{user.field.country}">Country</label>
                <select class="form-select"
                        id="editCountry"
                        name="country"
                        th:field="*{country}">
                    <option value="" th:text="#{user.form.select.country}">Select Country</option>
                    <option th:each="country : ${allCountries}"
                            th:value="${country.code}"
                            th:text="${country.name}">Country</option>
                </select>
                <div class="invalid-feedback"></div>
            </div>

            <!-- Language -->
            <div class="mb-3">
                <label for="editLanguage" class="form-label"><span th:text="#{user.field.language}">Language</span> *</label>
                <select class="form-select"
                        id="editLanguage"
                        name="language"
                        th:field="*{language}"
                        required>
                    <option value="" th:text="#{user.form.select.language}">Select Language</option>
                    <option th:each="language : ${allLanguages}"
                            th:value="${language.code}"
                            th:text="${language.name}">Language</option>
                </select>
                <div class="invalid-feedback" th:text="#{user.field.language.required}"></div>
            </div>

            <!-- City -->
            <div class="mb-3">
                <label for="editCity" class="form-label" th:text="#{user.field.city}">City</label>
                <input type="text"
                       class="form-control"
                       id="editCity"
                       name="city"
                       th:field="*{city}"
                       maxlength="100"
                       th:title="#{user.field.city.maxlength}">
                <div class="invalid-feedback" th:text="#{user.field.city.maxlength}"></div>
            </div>

            <!-- Work Format -->
            <div class="mb-3">
                <label for="editWorkFormat" class="form-label"><span th:text="#{user.field.workFormat}">Work Format</span> *</label>
                <select class="form-select"
                        id="editWorkFormat"
                        name="workFormat"
                        th:field="*{workFormat}"
                        required>
                    <option value="" th:text="#{user.form.select.workFormat}">Select Work Format</option>
                    <option th:each="workFormat : ${allWorkFormats}"
                            th:value="${workFormat.id}"
                            th:text="${workFormat.name}">Work Format</option>
                </select>
                <div class="invalid-feedback" th:text="#{user.field.workFormat.required}"></div>
            </div>

            <!-- Position -->
            <div class="mb-3">
                <label for="editPosition" class="form-label" th:text="#{user.field.position}">Position</label>
                <input type="text"
                       class="form-control"
                       id="editPosition"
                       name="position"
                       th:field="*{position}"
                       maxlength="100"
                       th:title="#{user.field.position.maxlength}">
                <div class="invalid-feedback" th:text="#{user.field.position.maxlength}"></div>
            </div>

            <!-- Joined Date -->
            <div class="mb-3">
                <label for="editJoinedDate" class="form-label" th:text="#{user.field.joinedDate}">Joined Date</label>
                <input type="date"
                       class="form-control"
                       id="editJoinedDate"
                       name="joinedDate"
                       th:field="*{joinedDate}">
                <div class="invalid-feedback"></div>
            </div>

            <!-- Status -->
            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input"
                           type="checkbox"
                           id="editEnabled"
                           name="enabled"
                           th:field="*{enabled}">
                    <label class="form-check-label" for="editEnabled" th:text="#{user.field.enabled}">
                        Account Enabled
                    </label>
                </div>
            </div>

            <!-- Account Locked -->
            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input"
                           type="checkbox"
                           id="editAccountLocked"
                           name="accountLocked"
                           th:field="*{accountLocked}">
                    <label class="form-check-label" for="editAccountLocked" th:text="#{user.field.accountLocked}">
                        Account Locked
                    </label>
                </div>
            </div>

            <!-- Roles -->
            <div class="mb-3">
                <label class="form-label">
                    <span th:text="#{user.field.roles}">Role</span>
                    <span th:if="!${isCurrentUserAdmin}"> *</span>
                </label>
                <div class="row" id="edit-roles-container">
                    <div th:each="role : ${allRoles}" class="col-md-4">
                        <div class="form-check">
                            <input class="form-check-input role-radio"
                                   type="radio"
                                   th:id="'editRole_' + ${role.name}"
                                   name="roles"
                                   th:value="${role.name}"
                                   th:checked="${editUserDto.roles != null && editUserDto.roles == role.name}"
                                   th:required="!${isCurrentUserAdmin}"
                                   onchange="validateRoleSelection('edit')">
                            <label class="form-check-label"
                                   th:for="'editRole_' + ${role.name}"
                                   th:text="${#strings.replace(role.name, 'ROLE_', '')}">
                                Role
                            </label>
                        </div>
                    </div>
                </div>
                <div class="invalid-feedback" id="editRolesInvalidFeedback" th:text="#{user.validation.roles.required}"></div>
            </div>
        </div>

        <div class="offcanvas-footer mt-4 pt-3 border-top">
            <button type="button"
                    class="btn btn-outline-secondary"
                    data-bs-dismiss="offcanvas"
                    th:text="#{user.button.cancel}">Cancel</button>
            <button type="submit"
                    class="btn btn-primary"
                    hx-indicator="#editSpinner">
                <span id="editSpinner" class="spinner-border spinner-border-sm htmx-indicator me-2" role="status"></span>
                <span th:text="#{user.button.update}">Update User</span>
            </button>
        </div>
    </form>
</div>

<style>
.password-requirements {
    font-size: 0.875rem;
}

.password-requirements .requirement {
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.password-requirements .requirement.valid i {
    color: #28a745 !important;
}

.password-requirements .requirement.valid i:before {
    content: "\ea5b"; /* ti-check icon */
}

.password-requirements .requirement.invalid i {
    color: #dc3545 !important;
}

.password-requirements .requirement.invalid i:before {
    content: "\eb55"; /* ti-x icon */
}

/* Role validation styles */
.role-radio.is-invalid {
    border-color: #dc3545;
}

.role-radio.is-invalid + .form-check-label {
    color: #dc3545;
}

#roles-container.is-invalid,
#edit-roles-container.is-invalid {
    border: 1px solid #dc3545;
    border-radius: 0.375rem;
    padding: 0.5rem;
    background-color: rgba(220, 53, 69, 0.05);
}

#rolesInvalidFeedback,
#editRolesInvalidFeedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

#rolesInvalidFeedback.d-block,
#editRolesInvalidFeedback.d-block {
    display: block !important;
}

/* Alert Box Styles */
#userFormAlert {
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#userFormAlert.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

#userFormAlert.alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #856404;
}

#userFormAlert.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

#userFormAlert.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

#userFormAlert .btn-close {
    padding: 0.5rem;
    margin: -0.25rem -0.25rem -0.25rem auto;
}

#userFormAlert i {
    font-size: 1.25rem;
    flex-shrink: 0;
}

#userFormAlertTitle {
    font-weight: 600;
    margin-bottom: 0.25rem;
    display: block;
}

#userFormAlertMessage {
    font-size: 0.875rem;
    line-height: 1.4;
}
</style>

<!-- JavaScript functions are now globally available from user-management.html -->

</body>
</html>
