<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
<div id="recentActivityList" th:fragment="recentActivityList" class="dataTables_wrapper dt-bootstrap5 no-footer">
    <table class="datatables-products table dataTable no-footer dtr-column" id="DataTables_Table_0"
           aria-describedby="DataTables_Table_0_info" style="width: 100%; table-layout: fixed;">
        <thead class="border-top">
        <!-- Filter Row -->
        <tr class="bg-primary-subtle">
            <th class="text-truncate" style="width: 25%;">
                <!-- No filter for browser -->
                <span th:text="#{table.header.filters}">Filtros</span>
            </th>
            <th class="text-truncate" style="width: 15%;">
                <!-- No filter for IP -->
            </th>
            <th class="text-truncate" style="width: 20%;">
                <!-- No filter for location -->
            </th>
            <th class="text-truncate" style="width: 20%;">
                <!-- Date Filter -->
                <input type="text"
                       id="dateFilter"
                       name="dateFilter"
                       class="form-control form-control-sm flatpickr-input date-picker-filter"
                       th:placeholder="#{filter.date.placeholder}"
                       th:title="#{filter.date.title}"
                       th:value="${currentDateFilter}"
                       hx-get="/user/securityUserPaged"
                       hx-target="#recentActivityList"
                       hx-swap="outerHTML"
                       hx-trigger="change"
                       hx-include="#activityFilter"
                       hx-vals='{"page": "1"}'>
            </th>
            <th class="text-truncate" style="width: 20%;">
                <!-- Activity Filter -->
                <div class="d-flex gap-1">
                    <select id="activityFilter"
                            name="activityFilter"
                            class="form-select form-select-sm flex-grow-1"
                            hx-get="/user/securityUserPaged"
                            hx-target="#recentActivityList"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="#dateFilter"
                            hx-vals='{"page": "1"}'>
                        <option value="all" th:text="#{filter.activity.all.events}" th:selected="${currentActivityFilter == 'all'}">All events</option>
                        <option th:each="activity : ${distinctActivities}"
                                th:value="${activity}"
                                th:text="${activity}"
                                th:selected="${activity == currentActivityFilter}"></option>
                    </select>
                    <button type="button"
                            id="clearFilters"
                            class="btn btn-sm btn-outline-secondary"
                            th:title="#{filter.clear.title}"
                            hx-get="/user/clearFilters"
                            hx-target="#recentActivityList"
                            hx-swap="outerHTML">
                        <i class="ti ti-x ti-sm"></i>
                    </button>
                </div>
            </th>
        </tr>
        <tr>
            <th class="text-truncate" style="width: 25%;" th:text="#{table.header.browser}">Navegador</th>
            <th class="text-truncate" style="width: 15%;" th:text="#{table.header.ip.address}">Endereço IP</th>
            <th class="text-truncate" style="width: 20%;" th:text="#{table.header.location}">Localização</th>
            <th class="text-truncate" style="width: 20%;" th:text="#{table.header.date.time}">Data/Hora</th>
            <th class="text-truncate" style="width: 20%;" th:text="#{table.header.event}">Evento</th>
        </tr>
        </thead>
        <tbody id="recentActivityListBody" th:attr="data-current-page=${currentActivityPage},data-total-records=${totalActivityRecords},data-total-pages=${totalActivityPages}">
        <!-- Real data rows -->
        <tr th:each="item, iterStat : ${recentActivityList}" th:class="${iterStat.odd} ? 'odd' : 'even'" role="row">
            <td class="text-truncate text-heading fw-medium dtr-control">
                <span th:if="${item.operatingSystem == 'Windows'}">
                    <i class='ti ti-brand-windows ti-md align-top text-info me-2'></i>
                </span>
                <span th:if="${item.operatingSystem == 'Mac'}">
                    <i class='ti ti-brand-finder ti-md align-top text-info me-2'></i>
                </span>
                <span th:if="${item.operatingSystem == 'Android'}">
                    <i class='ti ti-brand-android ti-md align-top text-info me-2'></i>
                </span>
                <span th:if="${item.operatingSystem == 'iOS'}">
                    <i class='ti ti-apple ti-md align-top text-info me-2'></i>
                </span>
                <span th:if="${item.operatingSystem == 'Linux'}">
                    <i class='ti ti-brand-ubuntu ti-md align-top text-info me-2'></i>
                </span>
                [[${item.browser}]] <span th:text="#{table.browser.on}">no</span> [[${item.operatingSystem}]]
            </td>
            <td class="text-truncate" th:text="${item.externalIp}"></td>
            <td class="text-truncate" th:text="${item.city} + '/'+ ${item.country}"></td>
            <td class="text-truncate" th:text="${item.dateTime}"></td>
            <td class="text-truncate"
                th:classappend="${item.activity == 'Login'} ? 'text-success' : (${item.activity == 'Logout'} ? 'text-danger' : '')"
                th:text="${#strings.isEmpty(item.activity)} ? '-' : ${item.activity}"></td>
        </tr>

        <!-- Empty placeholder rows to ensure always 10 rows total -->
        <tr th:if="${recentActivityList.size() < 10}" th:each="i : ${#numbers.sequence(1, 10 - recentActivityList.size())}" th:class="${recentActivityList.size() % 2 == 0 ? (i % 2 == 1 ? 'odd' : 'even') : (i % 2 == 0 ? 'odd' : 'even')}" role="row">
            <td class="text-truncate text-heading fw-medium dtr-control">...</td>
            <td class="text-truncate"></td>
            <td class="text-truncate"></td>
            <td class="text-truncate"></td>
            <td class="text-truncate"></td>
        </tr>
        </tbody>
    </table>
    <div class="row">
        <div class="col-sm-12 col-md-6">
            <div class="dataTables_info" id="DataTables_Table_0_info" role="status"
                 aria-live="polite">
                <span th:text="#{table.pagination.showing.from}">Mostrando de</span> [[(10 * ${currentActivityPage}) - 9]]
                <span th:text="#{table.pagination.to}">até</span> [[${currentActivityPage == totalActivityPages ? totalActivityRecords : 10 * currentActivityPage}]]
                <span th:text="#{table.pagination.of.total}">do total de</span> [[${totalActivityRecords}]]
                <span th:text="#{table.pagination.entries}">entradas</span>
            </div>
        </div>
        <div class="col-sm-12 col-md-6">
            <div class="dataTables_paginate paging_simple_numbers" id="DataTables_Table_0_paginate">
                <ul class="pagination pagination-sm">
                    <li class="paginate_button page-item first" id="DataTables_Table_0_first" th:classappend="${currentActivityPage == 1} ? 'disabled' : ''">
                        <a aria-controls="DataTables_Table_0"
                           aria-disabled="true" role="link"
                           data-dt-idx="first" tabindex="-1"
                           class="page-link"
                           th:hx-get="@{/user/securityUserPaged(page=1)}"
                           hx-target="#recentActivityList"
                           hx-swap="outerHTML"
                           hx-include="#dateFilter, #activityFilter">
                            <i class="ti ti-chevrons-left ti-sm"></i></a>
                    </li>
                    <li class="paginate_button page-item previous" id="DataTables_Table_0_previous" th:classappend="${currentActivityPage == 1} ? 'disabled' : ''">
                        <a aria-controls="DataTables_Table_0"
                           aria-disabled="true" role="link"
                           data-dt-idx="previous" tabindex="-1"
                           class="page-link"
                           th:hx-get="@{/user/securityUserPaged(page=${currentActivityPage}-1)}"
                           hx-target="#recentActivityList"
                           hx-swap="outerHTML"
                           hx-include="#dateFilter, #activityFilter">
                            <i class="ti ti-chevron-left ti-sm"></i></a>
                    </li>

                    <!-- If total pages <= 5, show all pages -->
                    <th:block th:if="${totalActivityPages <= 5}">
                        <th:block th:each="index : ${#numbers.sequence(1, totalActivityPages, 1)}">
                            <li class="paginate_button page-item" th:classappend="${index == currentActivityPage} ? 'active' : ''">
                                <a href="#"
                                   th:attr="aria-controls='DataTables_Table_0',
                                                aria-label='Page ' + ${index},
                                                data-dt-idx=${index}-1,
                                                tabindex=${index},
                                                data-page=${index},
                                                data-index=${index},
                                                aria-current=${index == currentActivityPage ? 'page' : null}"
                                   role="link"
                                   class="page-link"
                                   th:text="${index}"
                                   th:hx-get="@{/user/securityUserPaged(page=${index})}"
                                   hx-target="#recentActivityList"
                                   hx-swap="outerHTML"
                                   hx-include="#dateFilter, #activityFilter">
                                </a>
                            </li>
                        </th:block>
                    </th:block>

                    <!-- If total pages > 5, show dynamic pagination based on current page -->
                    <th:block th:if="${totalActivityPages > 5}">
                        <!-- Case 1: currentPage in first segment - Show first 5 pages, ellipsis, last page -->
                        <th:block th:if="${currentActivityPage <= 5}">
                            <!-- First 5 pages -->
                            <th:block th:each="index : ${#numbers.sequence(1, 5, 1)}">
                                <li class="paginate_button page-item" th:classappend="${index == currentActivityPage} ? 'active' : ''">
                                    <a href="#"
                                       th:attr="aria-controls='DataTables_Table_0',
                                                    aria-label='Page ' + ${index},
                                                    data-dt-idx=${index}-1,
                                                    tabindex=${index},
                                                    data-page=${index},
                                                    data-index=${index},
                                                    aria-current=${index == currentActivityPage ? 'page' : null}"
                                       role="link"
                                       class="page-link"
                                       th:text="${index}"
                                       th:hx-get="@{/user/securityUserPaged(page=${index})}"
                                       hx-target="#recentActivityList"
                                       hx-swap="outerHTML"
                                       hx-include="#dateFilter, #activityFilter">
                                    </a>
                                </li>
                            </th:block>

                            <!-- Ellipsis -->
                            <li class="paginate_button page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">...</a>
                            </li>

                            <!-- Last page -->
                            <li class="paginate_button page-item" th:classappend="${totalActivityPages == currentActivityPage} ? 'active' : ''">
                                <a href="#"
                                   th:attr="aria-controls='DataTables_Table_0',
                                                aria-label='Page ' + ${totalActivityPages},
                                                data-dt-idx=${totalActivityPages}-1,
                                                tabindex=${totalActivityPages},
                                                data-page=${totalActivityPages},
                                                data-index=${totalActivityPages},
                                                aria-current=${totalActivityPages == currentActivityPage ? 'page' : null}"
                                   role="link"
                                   class="page-link"
                                   th:text="${totalActivityPages}"
                                   th:hx-get="@{/user/securityUserPaged(page=${totalActivityPages})}"
                                   hx-target="#recentActivityList"
                                   hx-swap="outerHTML"
                                   hx-include="#dateFilter, #activityFilter">
                                </a>
                            </li>
                        </th:block>

                        <!-- Case 2: currentPage in middle segment - Show page 1, ellipsis, pages around current, ellipsis, last page -->
                        <th:block th:if="${currentActivityPage > 5 && currentActivityPage < totalActivityPages - 4}">
                            <!-- First page -->
                            <li class="paginate_button page-item" th:classappend="${1 == currentActivityPage} ? 'active' : ''">
                                <a href="#"
                                   th:attr="aria-controls='DataTables_Table_0',
                                                aria-label='Page 1',
                                                data-dt-idx=0,
                                                tabindex=1,
                                                data-page=1,
                                                data-index=1,
                                                aria-current=${1 == currentActivityPage ? 'page' : null}"
                                   role="link"
                                   class="page-link"
                                   th:text="1"
                                   th:hx-get="@{/user/securityUserPaged(page=1)}"
                                   hx-target="#recentActivityList"
                                   hx-swap="outerHTML"
                                   hx-include="#dateFilter, #activityFilter">
                                </a>
                            </li>

                            <!-- First Ellipsis -->
                            <li class="paginate_button page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">...</a>
                            </li>

                            <!-- Pages around current page (current-1, current, current+1) -->
                            <th:block th:each="index : ${#numbers.sequence(currentActivityPage - 1, currentActivityPage + 1, 1)}">
                                <li class="paginate_button page-item" th:classappend="${index == currentActivityPage} ? 'active' : ''">
                                    <a href="#"
                                       th:attr="aria-controls='DataTables_Table_0',
                                                    aria-label='Page ' + ${index},
                                                    data-dt-idx=${index}-1,
                                                    tabindex=${index},
                                                    data-page=${index},
                                                    data-index=${index},
                                                    aria-current=${index == currentActivityPage ? 'page' : null}"
                                       role="link"
                                       class="page-link"
                                       th:text="${index}"
                                       th:hx-get="@{/user/securityUserPaged(page=${index})}"
                                       hx-target="#recentActivityList"
                                       hx-swap="outerHTML"
                                       hx-include="#dateFilter, #activityFilter">
                                    </a>
                                </li>
                            </th:block>

                            <!-- Second Ellipsis -->
                            <li class="paginate_button page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">...</a>
                            </li>

                            <!-- Last page -->
                            <li class="paginate_button page-item" th:classappend="${totalActivityPages == currentActivityPage} ? 'active' : ''">
                                <a href="#"
                                   th:attr="aria-controls='DataTables_Table_0',
                                                aria-label='Page ' + ${totalActivityPages},
                                                data-dt-idx=${totalActivityPages}-1,
                                                tabindex=${totalActivityPages},
                                                data-page=${totalActivityPages},
                                                data-index=${totalActivityPages},
                                                aria-current=${totalActivityPages == currentActivityPage ? 'page' : null}"
                                   role="link"
                                   class="page-link"
                                   th:text="${totalActivityPages}"
                                   th:hx-get="@{/user/securityUserPaged(page=${totalActivityPages})}"
                                   hx-target="#recentActivityList"
                                   hx-swap="outerHTML"
                                   hx-include="#dateFilter, #activityFilter">
                                </a>
                            </li>
                        </th:block>

                        <!-- Case 3: currentPage in last segment - Show page 1, ellipsis, last 5 pages -->
                        <th:block th:if="${currentActivityPage >= totalActivityPages - 4}">
                            <!-- First page -->
                            <li class="paginate_button page-item" th:classappend="${1 == currentActivityPage} ? 'active' : ''">
                                <a href="#"
                                   th:attr="aria-controls='DataTables_Table_0',
                                                aria-label='Page 1',
                                                data-dt-idx=0,
                                                tabindex=1,
                                                data-page=1,
                                                data-index=1,
                                                aria-current=${1 == currentActivityPage ? 'page' : null}"
                                   role="link"
                                   class="page-link"
                                   th:text="1"
                                   th:hx-get="@{/user/securityUserPaged(page=1)}"
                                   hx-target="#recentActivityList"
                                   hx-swap="outerHTML"
                                   hx-include="#dateFilter, #activityFilter">
                                </a>
                            </li>

                            <!-- Ellipsis -->
                            <li class="paginate_button page-item disabled">
                                <a class="page-link" href="#" tabindex="-1">...</a>
                            </li>

                            <!-- Last 5 pages -->
                            <th:block th:each="index : ${#numbers.sequence(totalActivityPages - 4, totalActivityPages, 1)}">
                                <li class="paginate_button page-item" th:classappend="${index == currentActivityPage} ? 'active' : ''">
                                    <a href="#"
                                       th:attr="aria-controls='DataTables_Table_0',
                                                    aria-label='Page ' + ${index},
                                                    data-dt-idx=${index}-1,
                                                    tabindex=${index},
                                                    data-page=${index},
                                                    data-index=${index},
                                                    aria-current=${index == currentActivityPage ? 'page' : null}"
                                       role="link"
                                       class="page-link"
                                       th:text="${index}"
                                       th:hx-get="@{/user/securityUserPaged(page=${index})}"
                                       hx-target="#recentActivityList"
                                       hx-swap="outerHTML"
                                       hx-include="#dateFilter, #activityFilter">
                                    </a>
                                </li>
                            </th:block>
                        </th:block>
                    </th:block>
                    <li class="paginate_button page-item next" id="DataTables_Table_0_next" th:classappend="${currentActivityPage == totalActivityPages} ? 'disabled' : ''">
                        <a href="#"
                           aria-controls="DataTables_Table_0"
                           role="link"
                           data-dt-idx="next"
                           tabindex="0"
                           class="page-link"
                           th:hx-get="@{/user/securityUserPaged(page=${currentActivityPage}+1)}"
                           hx-target="#recentActivityList"
                           hx-swap="outerHTML"
                           hx-include="#dateFilter, #activityFilter">
                        <i class="ti ti-chevron-right ti-sm"></i>
                        </a>
                    </li>
                    <li class="paginate_button page-item first" id="DataTables_Table_0_last" th:classappend="${currentActivityPage == totalActivityPages} ? 'disabled' : ''">
                        <a aria-controls="DataTables_Table_0"
                           aria-disabled="true" role="link"
                           data-dt-idx="last" tabindex="-1"
                           class="page-link"
                           th:hx-get="@{/user/securityUserPaged(page=${totalActivityPages})}"
                           hx-target="#recentActivityList"
                           hx-swap="outerHTML"
                           hx-include="#dateFilter, #activityFilter">
                            <i class="ti ti-chevrons-right ti-sm"></i></a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div style="width: 1%;"></div>
</div>
</body>
</html>
