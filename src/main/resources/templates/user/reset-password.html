<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password</title>
    <!-- Favicon -->
    <link type="image/x-icon" rel="icon" th:href="@{/assets/img/favicon/favicon.ico}"/>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&amp;ampdisplay=swap" rel="stylesheet">

    <!-- Icons -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/fontawesome.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/tabler-icons.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/flag-icons.css}"/>

    <!-- Core CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/rtl/core.css}" class="template-customizer-core-css"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/rtl/theme-default.css}" class="template-customizer-theme-css"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/css/demo.css}"/>

    <!-- Vendors CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/node-waves/node-waves.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/typeahead-js/typeahead.css}"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f5f5f5;
            font-family: sans-serif;
        }
        .auth-wrapper {
            max-width: 500px; /* Increased width for more content */
            width: 100%;
            padding: 2rem;
            background: #fff;
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
        }
        .auth-inner h4 {
            margin-bottom: 1rem;
        }
        .auth-inner .form-text {
            font-size: 0.875em;
        }
        .password-requirements ul {
            padding-left: 1.2rem;
            font-size: 0.875em;
        }
        .password-requirements li {
            margin-bottom: 0.25rem;
        }
        .form-password-toggle .input-group-text {
            cursor: pointer;
        }
        .success-icon {
            color: #28a745;
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .error-icon {
            color: #dc3545;
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .alert-success {
            border-color: #d4edda;
            background-color: #d1ecf1;
        }
        .alert-danger {
            border-color: #f5c6cb;
            background-color: #f8d7da;
        }
        .invalid-feedback {
            display: none;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        .form-control.is-invalid ~ .invalid-feedback {
            display: block;
        }
        .was-validated .form-control:invalid ~ .invalid-feedback {
            display: block;
        }
    </style>
</head>
<body>

<div class="auth-wrapper">
    <div class="auth-inner">

        <!-- Success State: Password Reset Completed -->
        <div th:if="${success != null or completed != null}">
            <h4 class="text-center fw-bold text-success">[[#{page.user.reset.password.success.title}]]</h4>
            <div class="alert alert-success" role="alert">
                <div class="text-center">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="mb-2" th:text="${success != null ? success : 'Your password has been successfully reset!'}">Success message</p>
                    <p class="text-muted small">[[#{page.user.reset.password.success.submessage}]]</p>
                </div>
            </div>
        </div>

        <!-- Error State: Show error message only -->
        <div th:if="${error != null and success == null and completed == null}">
            <h4 class="text-center fw-bold text-danger">[[#{page.user.reset.password.error.title}]]</h4>
            <div class="alert alert-danger" role="alert">
                <div class="text-center">
                    <i class="fas fa-exclamation-circle fa-3x text-danger mb-3"></i>
                    <p class="mb-2" th:text="${error}">Error message</p>
                    <p class="text-muted small">[[#{page.user.reset.password.error.submessage}]]</p>
                </div>
            </div>
        </div>

        <!-- Default State: Show form -->
        <div th:if="${success == null and completed == null and error == null}">
            <h4 class="text-center fw-bold">[[#{page.user.reset.password.title}]]</h4>
            <p class="text-center mb-3">[[#{page.user.reset.password.subtitle}]]</p>

            <form id="formResetPassword" th:action="@{/reset-password}" th:object="${passwordResetDto}" method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="token" th:value="${token}" />

                <div class="mb-3 form-password-toggle">
                    <label class="form-label" for="newPassword">[[#{page.user.reset.password.input.password}]]</label>
                    <div class="input-group input-group-merge">
                        <input type="password"
                               th:field="*{newPassword}"
                               class="form-control"
                               th:classappend="${#fields.hasErrors('newPassword')} ? 'is-invalid' : ''"
                               id="newPassword"
                               placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                               aria-describedby="newPasswordHelp"
                               minlength="8"
                               pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@#$!%*?&.;:<>])[A-Za-z\d@#$!%*?&.;:<>]{8,}$"
                               th:title="#{validation.password.pattern}"
                               oninput="passwordMatching()"
                               onchange="passwordMatching()"
                               required/>
                        <span class="input-group-text"><i class="fas fa-eye-slash" id="toggleNewPassword"></i></span>
                        <div class="invalid-feedback" id="invalid-feedback-password">
                            <span th:if="${#fields.hasErrors('newPassword')}" th:errors="*{newPassword}"></span>
                        </div>
                    </div>
                     <div id="newPasswordHelp" class="form-text">
                        [[#{page.user.reset.password.input.password.hint}]]
                    </div>
                </div>

                <div class="mb-3 form-password-toggle">
                    <label class="form-label" for="confirmPassword">[[#{page.user.reset.password.input.confirmation}]]</label>
                    <div class="input-group input-group-merge">
                        <input type="password" th:field="*{confirmPassword}" class="form-control" th:classappend="${#fields.hasErrors('confirmPassword')} ? 'is-invalid' : ''" id="confirmPassword"
                               placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                               oninput="passwordMatching()" onchange="passwordMatching()" required />
                        <span class="input-group-text"><i class="fas fa-eye-slash" id="toggleConfirmPassword"></i></span>
                        <div class="invalid-feedback" id="invalid-feedback-confirmation">
                            <span th:if="${#fields.hasErrors('confirmPassword')}" th:errors="*{confirmPassword}"></span>
                        </div>
                    </div>
                </div>

                <div class="mb-3 password-requirements">
                    <h6 class="text-body mt-8">[[#{page.user.reset.password.requirements}]]</h6>
                    <ul class="ps-4 mb-0">
                        <li class="mb-2">[[#{page.user.security.password.policies.01}]]</li>
                        <li class="mb-2">[[#{page.user.security.password.policies.02}]]</li>
                        <li class="mb-2">[[#{page.user.security.password.policies.03}]]</li>
                        <li class="mb-2">[[#{page.user.security.password.policies.04}]]</li>
                        <li class="mb-2">[[#{page.user.security.password.policies.05}]]</li>
                        <li class="mb-2">[[#{page.user.security.password.policies.06}]]</li>
                    </ul>
                </div>

                <button class="btn btn-primary w-100  mt-10" type="submit">[[#{page.user.reset.password.button.reset}]]</button>

                <!-- Hidden inputs for internationalized messages -->
                <input type="hidden" id="passwordReq01" th:value="#{page.user.security.change.password.req.01}">
                <input type="hidden" id="passwordReq02" th:value="#{page.user.security.change.password.req.02}">
                <input type="hidden" id="passwordReq03" th:value="#{page.user.security.change.password.req.03}">
                <input type="hidden" id="passwordReq04" th:value="#{page.user.security.change.password.req.04}">
                <input type="hidden" id="passwordReq05" th:value="#{page.user.security.change.password.req.05}">
                <input type="hidden" id="passwordReq06" th:value="#{page.user.security.change.password.req.06}">
                <input type="hidden" id="passwordReq07" th:value="#{page.user.security.change.password.req.07}">
                <input type="hidden" id="passwordReq09" th:value="#{page.user.security.change.password.req.09}">
            </form>
        </div>

        <!-- Back to login link - always visible -->
        <p class="text-center mt-3">
            <a th:href="@{/login}">
                <i class="fas fa-arrow-left me-2"></i>[[#{page.user.reset.password.button.back}]]
            </a>
        </p>
    </div>
</div>

<script th:src="@{/assets/vendor/js/bootstrap.js}"></script>
<script th:inline="javascript">
    document.addEventListener('DOMContentLoaded', function () {
        // Only run form-related JavaScript if the form exists (not in success/error state)
        const form = document.getElementById('formResetPassword');
        if (!form) return;

        const togglePasswordVisibility = function (inputId, toggleId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(toggleId);
            if (!passwordInput || !toggleIcon) return;

            toggleIcon.addEventListener('click', function () {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.classList.toggle('fa-eye');
                this.classList.toggle('fa-eye-slash');
            });
        };

        togglePasswordVisibility('newPassword', 'toggleNewPassword');
        togglePasswordVisibility('confirmPassword', 'toggleConfirmPassword');

        // Add event listeners for real-time validation
        const newPasswordInput = document.getElementById('newPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');

        if (newPasswordInput) {
            newPasswordInput.addEventListener('input', passwordMatching);
            newPasswordInput.addEventListener('blur', passwordMatching);
        }

        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', passwordMatching);
            confirmPasswordInput.addEventListener('blur', passwordMatching);
        }

        // Password validation function
        function passwordMatching() {
            const password = document.querySelector('input[name=newPassword]');
            const confirm = document.querySelector('input[name=confirmPassword]');

            if (!password || !confirm) return;

            // Validate password strength
            const message = checkPasswordValidity(password.value);
            if (message) {
                password.setCustomValidity('x');
                password.classList.add('is-invalid');
                password.classList.remove('is-valid');
                const feedbackElement = document.getElementById("invalid-feedback-password");
                if (feedbackElement) {
                    feedbackElement.innerHTML = message;
                }
            } else {
                password.setCustomValidity('');
                password.classList.remove('is-invalid');
                password.classList.add('is-valid');
                const feedbackElement = document.getElementById("invalid-feedback-password");
                if (feedbackElement) {
                    feedbackElement.innerHTML = '';
                }

                // Check password confirmation matching
                if (confirm.value && confirm.value !== password.value) {
                    confirm.setCustomValidity('x');
                    confirm.classList.add('is-invalid');
                    confirm.classList.remove('is-valid');
                    const confirmFeedbackElement = document.getElementById("invalid-feedback-confirmation");
                    if (confirmFeedbackElement) {
                        confirmFeedbackElement.innerHTML = document.getElementById('passwordReq09')?.value || "The password entered does not match!";
                    }
                } else {
                    confirm.setCustomValidity('');
                    confirm.classList.remove('is-invalid');
                    if (confirm.value) {
                        confirm.classList.add('is-valid');
                    }
                    const confirmFeedbackElement = document.getElementById("invalid-feedback-confirmation");
                    if (confirmFeedbackElement) {
                        confirmFeedbackElement.innerHTML = '';
                    }
                }
            }
        }

        // Password validation rules
        const checkPasswordValidity = (value) => {
            if (!value) return null;

            const isNonWhiteSpace = /^\S*$/;
            if (!isNonWhiteSpace.test(value)) {
                return document.getElementById('passwordReq01')?.value || "The new password cannot contain spaces";
            }

            const isContainsUppercase = /^(?=.*[A-Z]).*$/;
            if (!isContainsUppercase.test(value)) {
                return document.getElementById('passwordReq02')?.value || "The new password must have at least one uppercase character";
            }

            const isContainsLowercase = /^(?=.*[a-z]).*$/;
            if (!isContainsLowercase.test(value)) {
                return document.getElementById('passwordReq03')?.value || "New password must have at least one lowercase character";
            }

            const isContainsNumber = /^(?=.*[0-9]).*$/;
            if (!isContainsNumber.test(value)) {
                return document.getElementById('passwordReq04')?.value || "The new password must contain at least one number from 0 to 9";
            }

            const isContainsSymbol = /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/;
            if (!isContainsSymbol.test(value)) {
                return document.getElementById('passwordReq05')?.value || "New password must contain at least one special character";
            }

            const isValidLength = /^.{8,}$/;
            if (!isValidLength.test(value)) {
                return document.getElementById('passwordReq06')?.value || "The new password must be at least 8 characters long";
            }

            const numericSequenceRegex = /\d{4,}/;
            if (numericSequenceRegex.test(value)) {
                return document.getElementById('passwordReq07')?.value || "The new password must not contain sequences of 4 or more numbers";
            }

            // Check if password contains the word "password"
            if (value.toLowerCase().includes('password')) {
                return "Password cannot contain the word 'password'";
            }

            return null;
        }

        // Make passwordMatching function globally available
        window.passwordMatching = passwordMatching;

        // Enhanced form submission validation
        form.addEventListener('submit', function (event) {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');

            // Validate password strength
            const passwordValidationMessage = checkPasswordValidity(newPassword);
            if (passwordValidationMessage) {
                newPasswordInput.setCustomValidity('x');
                newPasswordInput.classList.add('is-invalid');
                newPasswordInput.classList.remove('is-valid');
                const feedbackElement = document.getElementById("invalid-feedback-password");
                if (feedbackElement) {
                    feedbackElement.innerHTML = passwordValidationMessage;
                }
                event.preventDefault();
                event.stopPropagation();
            } else {
                newPasswordInput.setCustomValidity('');
                newPasswordInput.classList.remove('is-invalid');
                newPasswordInput.classList.add('is-valid');
            }

            // Validate password confirmation
            if (newPassword !== confirmPassword) {
                confirmPasswordInput.setCustomValidity('x');
                confirmPasswordInput.classList.add('is-invalid');
                confirmPasswordInput.classList.remove('is-valid');
                const confirmFeedbackElement = document.getElementById("invalid-feedback-confirmation");
                if (confirmFeedbackElement) {
                    confirmFeedbackElement.innerHTML = document.getElementById('passwordReq09')?.value || "The password entered does not match!";
                }
                event.preventDefault();
                event.stopPropagation();
            } else {
                confirmPasswordInput.setCustomValidity('');
                confirmPasswordInput.classList.remove('is-invalid');
                if (confirmPassword) {
                    confirmPasswordInput.classList.add('is-valid');
                }
            }

            // Add was-validated for Bootstrap styles if not already present
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
</script>
</body>
</html>
