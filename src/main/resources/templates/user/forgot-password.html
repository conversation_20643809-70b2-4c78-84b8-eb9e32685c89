<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password</title>
    <!-- Add your CSS links here -->
    <!-- Example: <link rel="stylesheet" th:href="@{/css/style.css}"> -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f5f5f5;
            font-family: sans-serif;
        }
        .auth-wrapper {
            max-width: 400px;
            width: 100%;
            padding: 2rem;
            background: #fff;
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
        }
        .auth-inner h4 {
            margin-bottom: 1rem;
        }
        .auth-inner p {
            margin-bottom: 1.5rem;
            color: #6c757d;
        }
        .form-label {
            font-weight: 500;
        }
        .success-icon {
            color: #28a745;
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .alert-success {
            border-color: #d4edda;
            background-color: #d1ecf1;
        }
        .htmx-indicator {
            display: none;
        }
        .htmx-request .htmx-indicator {
            display: inline-block;
        }
    </style>
</head>
<body>

<div class="auth-wrapper">
    <div class="auth-inner">
        <div class="text-center mb-4">
            <!-- You can place a logo here if you have one -->
            <!-- <img th:src="@{/images/logo.png}" alt="Logo" style="width: 150px;"> -->
        </div>

        <!-- Show different content based on success state -->
        <div th:if="${success}">
            <!-- Success state: Show confirmation message -->
            <h4 class="text-center fw-bold text-success">[[#{page.user.forgot.password.success.title}]]</h4>
            <div class="alert alert-success" role="alert">
                <div class="text-center">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="mb-2" th:text="${success}">Success message</p>
                    <p class="text-muted small fw-bold mt-4">[[#{page.user.forgot.password.success.message}]]</p>
                </div>
            </div>
        </div>

        <div th:unless="${success}">
            <!-- Default state: Show form -->
            <h4 class="text-center fw-bold">[[#{page.user.forgot.password.title}]]</h4>
            <p class="text-center">[[#{page.user.forgot.password.subtitle}]]</p>

            <div th:if="${error}" class="alert alert-danger" role="alert">
                <p th:text="${error}">Error message</p>
            </div>

            <form class="auth-forgot-password-form mt-2" th:action="@{/forgot-password}" method="POST" th:hx-post="@{/forgot-password}" hx-trigger="submit" hx-target=".auth-wrapper" hx-swap="outerHTML">
                <div class="mb-3">
                    <label for="email" class="form-label">[[#{page.user.forgot.password.input.email}]]</label>
                    <input type="email" class="form-control" id="email" name="email" th:placeholder="#{page.user.forgot.password.input.email.placeholder}" autofocus required />
                </div>
                <button class="btn btn-primary w-100" type="submit">
                    <span class="spinner-border spinner-border-sm text-white htmx-indicator me-2" role="status" aria-hidden="true"></span>
                    [[#{page.user.forgot.password.button.send}]]
                </button>
            </form>
        </div>

        <p class="text-center mt-3">
            <a th:href="@{/login}">
                <i class="bx bx-chevron-left scaleX-n1-rtl bx-sm"></i> <!-- Assuming you have Boxicons or similar -->
                [[#{page.user.forgot.password.button.back}]]
            </a>
        </p>
    </div>
</div>

<!-- Add your JS links here -->
<!-- Example: <script th:src="@{/js/script.js}"></script> -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/htmx-2.0.2.js}"></script>
</body>
</html>
