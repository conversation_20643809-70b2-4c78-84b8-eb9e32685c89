<!doctype html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>

    <title>[[#{page.user.security.title}]]</title>

    <style>
        .htmx-settling img {
            opacity: 0;
        }

        img {
            transition: opacity 300ms ease-in;
        }
    </style>
</head>
<body>

<th:block layout:fragment="optionalVendorsCSS">
    <!-- My Vendor CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css}"/>
</th:block>

<th:block layout:fragment="optionalPageCSS">
    <!-- My Page CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/select2/select2.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/@form-validation/form-validation.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/animate-css/animate.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/sweetalert2/sweetalert2.css}"/>

    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/flatpickr/flatpickr.css}"/>
</th:block>

<div layout:fragment="content">

    <div class="row">
        <div class="col-md-12">
            <div class="nav-align-top">
                <ul class="nav nav-pills flex-column flex-md-row mb-6 gap-2 gap-lg-0">
                    <li class="nav-item"><a class="nav-link" th:href="@{/user/edituser}"><i
                            class="ti-sm ti ti-users me-1_5"></i> [[#{page.user.my.account}]]</a></li>
                    <li class="nav-item"><a class="nav-link active" href="javascript:void(0);"><i
                            class="ti-sm ti ti-lock me-1_5"></i> [[#{page.user.security}]]</a></li>
                </ul>
            </div>
            <!-- Change Password -->
            <div class="card mb-6">
                <h5 class="card-header">[[#{user.security.change.password.title}]]</h5>
                <div hx-ext="client-side-templates" class="card-body pt-1">
                    <form id="formAccountSettings"
                          hx-post="/users/changepassword"
                          hx-swap="innerHTML"
                          hx-target="#response"
                          hx-on::after-request="this.reset(); this.classList.remove('was-validated'); ResultModalMsg()"
                          hx-on:htmx:response-error="ModalError()"
                          hx-trigger="bs-send"
                          enctype="multipart/form-data"
                          class="row g-2 needs-validation"
                          novalidate
                          th:data-req02="#{page.user.security.change.password.req.02}">

                        <div class="row">
                            <div class="mb-6 col-md-6 form-password-toggle">
                                <label class="form-label" for="currentPassword">[[#{user.security.current.password.label}]]</label>
                                <div class="input-group input-group-merge">
                                    <input class="form-control" type="password" name="currentPassword" required
                                           id="currentPassword"
                                           placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"/>
                                    <span class="input-group-text cursor-pointer"><i class="ti ti-eye-off"></i></span>
                                    <div class="invalid-feedback">
                                        [[#{user.validation.current.password.required}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="mb-6 col-md-6 form-password-toggle">
                                <label class="form-label" for="newPassword">[[#{user.security.new.password.label}]]</label>
                                <div class="input-group input-group-merge">
                                    <input type="password" name="newPassword" class="form-control" id="newPassword"
                                           required
                                           placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                           onChange="passwordMatching()"/>
                                    <span class="input-group-text cursor-pointer"><i class="ti ti-eye-off"></i></span>
                                    <div class="invalid-feedback" id="invalid-feedback">
                                        [[#{user.validation.new.password.required}]]
                                    </div>
                                </div>
                            </div>

                            <div class="mb-6 col-md-6 form-password-toggle">
                                <label class="form-label" for="newPasswordConfirm">[[#{user.security.confirm.new.password.label}]]</label>
                                <div class="input-group input-group-merge">
                                    <input type="password" name="newPasswordConfirm" class="form-control"
                                           id="newPasswordConfirm" required
                                           placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                           onChange="passwordMatching()"/>
                                    <span class="input-group-text cursor-pointer"><i class="ti ti-eye-off"></i></span>
                                    <div class="invalid-feedback" id="invalid-feedback-confirmation">
                                        [[#{user.validation.confirm.password.required}]]
                                    </div>
                                </div>
                            </div>
                        </div>
                        <h6 class="text-body">[[#{user.security.password.requirements.title}]]</h6>
                        <ul class="ps-4 mb-0">
                            <li class="mb-2">[[#{page.user.security.password.policies.01}]]</li>
                            <li class="mb-2">[[#{page.user.security.password.policies.02}]]</li>
                            <li class="mb-2">[[#{page.user.security.password.policies.03}]]</li>
                            <li class="mb-2">[[#{page.user.security.password.policies.04}]]</li>
                            <li class="mb-2">[[#{page.user.security.password.policies.05}]]</li>
                            <li class="mb-2">[[#{page.user.security.password.policies.06}]]</li>
                        </ul>
                        <div class="mt-6">
                            <button type="submit" class="btn btn-primary me-3">[[#{user.button.save.changes}]]</button>
                            <button type="reset" class="btn btn-label-secondary">[[#{user.button.reset}]]</button>
                        </div>
                        <input type="hidden" id="passwordReq02" th:value="#{page.user.security.change.password.req.02}">
                    </form>

                    <div id="response" class="text-bg-danger mt-5" hidden>
                        <span id="responseMsg" th:text="${response}"></span>
                    </div>

                </div>
            </div>
            <!--/ Change Password -->

            <!-- Recent Devices -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">[[#{user.security.recent.activity.title}]]</h5>
                </div>
                <div class="card-datatable table-responsive">
                    <div id="recentActivityList" th:replace="~{user/fragments/recent-activity-list :: recentActivityList}"></div>
                </div>
            </div>
            <!--/ Recent Devices -->
        </div>
    </div>

</div>

<th:block layout:fragment="optionalVendorJS">
    <script th:src="@{/assets/vendor/libs/sweetalert2/sweetalert2.js}"></script>
    <script th:src="@{/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js}"></script>
    <script th:src="@{/assets/vendor/libs/flatpickr/flatpickr.js}"></script>
    <script th:src="@{/assets/vendor/libs/flatpickr/pt.js}"></script>
</th:block>

<th:block layout:fragment="optionalPageJS">
    <script th:src="@{/js/htmx-extensions/bsSend.js}"></script>
    <script th:src="@{/js/modalResponse.js}"></script>
    <script th:src="@{/js/pages/recent-activity-filters.js}"></script>

    <script th:inline="javascript">
        // Update pagination UI after htmx loads new content
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.id === 'recentActivityListBody') {
                // Get pagination data from data attributes
                const tbody = event.detail.target;
                const currentPage = parseInt(tbody.getAttribute('data-current-page'));

                // Update active page in pagination
                const paginationItems = document.querySelectorAll('.pagination .page-item');
                paginationItems.forEach(item => {
                    // Skip previous and next buttons
                    if (item.id === 'DataTables_Table_0_previous' || item.id === 'DataTables_Table_0_next') {
                        return;
                    }

                    const link = item.querySelector('a');
                    if (link && link.textContent.trim() === currentPage.toString()) {
                        item.classList.add('active');
                        link.setAttribute('aria-current', 'page');
                    } else if (link && link.textContent.trim() !== '') {
                        item.classList.remove('active');
                        link.removeAttribute('aria-current');
                    }
                });
            }
        });
        // Re-apply DataTables styling after htmx loads new content
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.id === 'recentActivityList') {
                // Apply alternating row classes
                const rows = event.detail.target.querySelectorAll('tr');
                rows.forEach((row, index) => {
                    row.classList.remove('odd', 'even');
                    row.classList.add(index % 2 === 0 ? 'odd' : 'even');
                    row.setAttribute('role', 'row');
                });
            }
        });

        function passwordMatching() {
            const currentPassword = document.querySelector('input[name=currentPassword]');
            const password = document.querySelector('input[name=newPassword]');
            const confirm = document.querySelector('input[name=newPasswordConfirm]');

            if (currentPassword.value === password.value) {
                password.setCustomValidity('x');
                document.getElementById("invalid-feedback").innerHTML = /*[[#{page.user.security.change.password.req.08}]]*/"page.user.security.change.password.req.08";
            } else {
                password.setCustomValidity('');
                confirm.setCustomValidity('');
                const message = checkPasswordValidity(password.value);
                if (message) {
                    password.setCustomValidity('x');
                    document.getElementById("invalid-feedback").innerHTML = message;
                } else {
                    if (confirm.value === password.value) {
                        password.setCustomValidity('');
                        confirm.setCustomValidity('');
                    } else {
                        confirm.setCustomValidity('x');
                        document.getElementById("invalid-feedback-confirmation").innerHTML = /*[[#{page.user.security.change.password.req.09}]]*/"page.user.security.change.password.req.09";
                    }
                }
            }
        }

        const checkPasswordValidity = (value) => {
            const isNonWhiteSpace = /^\S*$/;
            if (!isNonWhiteSpace.test(value)) {
                return /*[[#{page.user.security.change.password.req.01}]]*/ "page.user.security.change.password.req.01";
            }

            const isContainsUppercase = /^(?=.*[A-Z]).*$/;
            if (!isContainsUppercase.test(value)) {
                return /*[[#{page.user.security.change.password.req.02}]]*/ "page.user.security.change.password.req.02";
            }

            const isContainsLowercase = /^(?=.*[a-z]).*$/;
            if (!isContainsLowercase.test(value)) {
                return /*[[#{page.user.security.change.password.req.03}]]*/ "page.user.security.change.password.req.03";
            }

            const isContainsNumber = /^(?=.*[0-9]).*$/;
            if (!isContainsNumber.test(value)) {
                return /*[[#{page.user.security.change.password.req.04}]]*/ "page.user.security.change.password.req.04";
            }

            const isContainsSymbol =
                /^(?=.*[~`!@#$%^&*()--+={}\[\]|\\:;"'<>,.?/_₹]).*$/;
            if (!isContainsSymbol.test(value)) {
                return /*[[#{page.user.security.change.password.req.05}]]*/ "page.user.security.change.password.req.05";
            }

            const isValidLength = /^.{8,}$/;
            if (!isValidLength.test(value)) {
                return /*[[#{page.user.security.change.password.req.06}]]*/ "page.user.security.change.password.req.06";
            }

            const numericSequenceRegex = /\d{4,}/;
            if (numericSequenceRegex.test(value)) {
                return /*[[#{page.user.security.change.password.req.07}]]*/ "page.user.security.change.password.req.07";
            }

            return null;
        }

        // Add event listener for Reset button to clear invalid feedback
        document.addEventListener('DOMContentLoaded', function() {
            const resetButton = document.querySelector('button[type="reset"]');
            const form = document.getElementById('formAccountSettings');

            if (resetButton && form) {
                resetButton.addEventListener('click', function() {
                    // Clear all invalid-feedback elements
                    const invalidFeedbackElements = form.querySelectorAll('.invalid-feedback');
                    invalidFeedbackElements.forEach(element => {
                        element.innerHTML = '';
                    });

                    // Clear custom validity on all form inputs
                    const formInputs = form.querySelectorAll('input');
                    formInputs.forEach(input => {
                        input.setCustomValidity('');
                    });

                    // Remove validation classes from form
                    form.classList.remove('was-validated');

                    // Remove is-invalid class from all form controls
                    const formControls = form.querySelectorAll('.form-control');
                    formControls.forEach(control => {
                        control.classList.remove('is-invalid');
                    });
                });
            }
        });
    </script>
</th:block>

</body>
</html>
