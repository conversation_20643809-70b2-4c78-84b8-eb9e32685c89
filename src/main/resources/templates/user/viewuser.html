<!doctype html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>

    <title>[[#{page.user.profile.title}]]</title>

</head>
<body>

<th:block layout:fragment="optionalPageCSS">
    <!-- Page CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/pages/page-profile.css}"/>
</th:block>

<div layout:fragment="content">

    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-6">
                <div class="user-profile-header-banner">
                    <img src="../../assets/img/pages/profile-banner.png" alt="Banner image" class="rounded-top">
                </div>
                <div class="user-profile-header d-flex flex-column flex-lg-row text-sm-start text-center mb-5">
                    <div class="flex-shrink-0 mt-n2 mx-sm-0 mx-auto">
                        <img th:src="${currentUser.getPhoto()} ? ${'/images/' + currentUser.getPhoto()} :  '../../assets/img/avatars/silhouette.png'" alt="user image"
                             class="d-block h-auto ms-0 ms-sm-6 rounded user-profile-img">
                    </div>
                    <div class="flex-grow-1 mt-3 mt-lg-5">
                        <div class="d-flex align-items-md-end align-items-sm-start align-items-center justify-content-md-between justify-content-start mx-5 flex-md-row flex-column gap-4">
                            <div class="user-profile-info">
                                <h4 class="mb-2 mt-lg-6"
                                    th:text="${currentUser.getFullName()} ? ${currentUser.getFullName()} : #{page.content.not.defined}"></h4>
                                <ul class="list-inline mb-0 d-flex align-items-center flex-wrap justify-content-sm-start justify-content-center gap-4 my-2">
                                    <li class="list-inline-item d-flex gap-2 align-items-center">
                                        <i class='ti ti-building ti-lg' th:title="#{user.tooltip.company}"></i><span
                                            th:title="#{user.tooltip.company}"
                                            class="fw-medium"
                                            th:text="${currentUser.getCompany()} ? ${currentUser.getCompany()} : #{page.content.not.defined}"></span>
                                    </li>
                                    <li class="list-inline-item d-flex gap-2 align-items-center">
                                        <i class='ti ti-crown ti-lg' th:title="#{user.tooltip.position}"></i><span
                                            th:title="#{user.tooltip.position}"
                                            class="fw-medium"
                                            th:text="${currentUser.getPosition()} ? ${currentUser.getPosition()} : #{page.content.not.defined}"></span>
                                    </li>
                                    <li class="list-inline-item d-flex gap-2 align-items-center">
                                        <i class='ti ti-chair-director ti-lg' th:title="#{user.tooltip.work.format}"></i><span
                                            th:title="#{user.tooltip.work.format}"
                                            class="fw-medium"
                                            th:text="${currentUser.getWork_format()} ? ${workFormatName} : #{page.content.not.defined}"></span>
                                    </li>
                                    <li class="list-inline-item d-flex gap-2 align-items-center">
                                        <i class='ti ti-calendar ti-lg' th:title="#{user.tooltip.joined.date}"></i><span
                                            th:title="#{user.tooltip.joined.date}"
                                            class="fw-medium"
                                            th:text="${currentUser.getJoined_date()} ? ${currentUser.getJoined_date()} : #{page.content.not.defined}"></span>
                                    </li>
                                </ul>
                            </div>
                            <th:block th:if="${#authorization.expression('isAuthenticated()')}">
                                <a href="javascript:void(0)" class="btn btn-primary mb-1">
                                    <i class='ti ti-user-check ti-xs me-2'></i>[[#{user.status.online}]]
                                </a>
                            </th:block>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--/ Header -->

    <!-- Navbar pills -->
    <div class="row">
        <div class="col-md-12">
            <div class="nav-align-top">
                <ul class="nav nav-pills flex-column flex-sm-row mb-6 gap-2 gap-lg-0"
                    hx-on:htmx-after-on-load="
                           let currentTab = document.querySelector('[aria-selected=true]');
                           currentTab.setAttribute('aria-selected', 'false')
                           currentTab.classList.remove('active')
                           let newTab = event.target
                           newTab.setAttribute('aria-selected', 'true')
                           newTab.classList.add('active')"
                >

                    <li class="nav-item">
                        <a class="nav-link active" aria-selected="true"
                           th:hx-get="@{/user/user-profile}"
                           hx-swap="innerHTML"
                           hx-target="#viewUserDetails">
                            <i class='ti-sm ti ti-user-check me-1_5'></i> [[#{user.tab.profile}]]</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" aria-selected="false"
                           th:hx-get="@{/user/user-projects}"
                           hx-swap="innerHTML"
                           hx-target="#viewUserDetails">
                            <i class='ti-sm ti ti-layout-grid me-1_5'></i> [[#{user.tab.projects}]]</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <!--/ Navbar pills -->

    <div id="viewUserDetails">
        <div th:replace="~{user/fragments/user-profile :: user-profile}"></div>
    </div>
</div>

</body>
</html>