<!DOCTYPE html>
<html th:lang="${#locale.language}"
      lass="light-style layout-wide  customizer-hide"
      dir="ltr"
      data-theme="theme-default"
      data-assets-path="../../assets/"
      data-template="vertical-menu-template"
      data-style="light"
      xmlns:th="http://www.thymeleaf.org" th:fragment="layout(content)">


<!-- Mirrored from demos.pixinvent.com/vuexy-html-admin-template/html/vertical-menu-template/auth-login-cover.html by HTTrack Website Copier/3.x [XR&CO'2014], Wed, 18 Sep 2024 18:14:08 GMT -->
<head>
    <meta charset="utf-8"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0"/>

    <title>Login Cover - Pages | Vuexy - Bootstrap Admin Template</title>


    <meta name="description" content="Start your development with a Dashboard for Bootstrap 5"/>
    <meta name="keywords" content="dashboard, bootstrap 5 dashboard, bootstrap 5 design, bootstrap 5">
    <!-- Canonical SEO -->
    <link rel="canonical" href="https://1.envato.market/vuexy_admin">


    <!-- Favicon -->
    <link type="image/x-icon" rel="icon" th:href="@{/assets/img/favicon/favicon.ico}"/>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&amp;ampdisplay=swap"
          rel="stylesheet">

    <!-- Icons -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/fontawesome.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/tabler-icons.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/flag-icons.css}"/>

    <!-- Core CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/rtl/core.css}"
          class="template-customizer-core-css"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/rtl/theme-default.css}"
          class="template-customizer-theme-css"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/css/demo.css}"/>

    <!-- Vendors CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/node-waves/node-waves.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/typeahead-js/typeahead.css}"/>

    <!-- Vendor -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/%40form-validation/form-validation.css}"/>
    <!-- Page CSS -->
    <!-- Page -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/pages/page-auth.css}"/>

    <!-- Helpers -->
    <script th:src="@{/assets/vendor/js/helpers.js}"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->

    <!--? Template customizer: To hide customizer set displayCustomizer value false in config.js.  -->
    <script th:src="@{/assets/vendor/js/template-customizer.js}"></script>

    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script th:src="@{/assets/js/config.js}"></script>

    <!-- reCAPTCHA script -->
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>

</head>

<body>


<!-- Content -->

<div class="authentication-wrapper authentication-cover">
    <!-- Logo -->
    <a th:href="@{/}" class="app-brand auth-cover-brand">
    <span class="app-brand-logo demo">
<!--<svg width="32" height="22" viewBox="0 0 32 22" fill="none" xmlns="http://www.w3.org/2000/svg">-->
<img th:src="@{/assets/img/logo/jobify-icon.svg}" alt="Jobify Logo">

    </span>
        <span class="app-brand-text demo text-heading fw-bold">Jobify</span>
    </a>
    <!-- /Logo -->
    <div class="authentication-inner row m-0">
        <!-- /Left Text -->
        <div class="d-none d-lg-flex col-lg-8 p-0">
            <div class="auth-cover-bg auth-cover-bg-color d-flex justify-content-center align-items-center">
                <img src="../../assets/img/illustrations/auth-login-illustration-light.png" alt="auth-login-cover"
                     class="my-5 auth-illustration" data-app-light-img="illustrations/auth-login-illustration-light.png"
                     data-app-dark-img="illustrations/auth-login-illustration-dark.html">

                <img src="../../assets/img/illustrations/bg-shape-image-light.png" alt="auth-login-cover"
                     class="platform-bg" data-app-light-img="illustrations/bg-shape-image-light.png"
                     data-app-dark-img="illustrations/bg-shape-image-dark.html">
            </div>
        </div>
        <!-- /Left Text -->

        <!-- Login -->
        <div class="d-flex col-12 col-lg-4 align-items-center authentication-bg p-sm-12 p-6">
            <div class="w-px-400 mx-auto mt-12 pt-5">
                <h4 class="mb-1">[[#{page.login.title}]]</h4>
                <p class="mb-6">[[#{page.login.subtitle}]]</p>

                <form id="formAuthentication" class="mb-6"
                      th:action="@{/token}" method="post">
                    <div class="mb-6">
                        <label for="email" class="form-label">[[#{page.login.email}]]</label>
                        <input type="email" class="form-control" id="email" name="email"
                               placeholder="Enter your email or username" autofocus>

                    </div>
                    <div class="mb-6 form-password-toggle">
                        <label class="form-label" for="password">[[#{page.login.password}]]</label>
                        <div class="input-group input-group-merge">
                            <input type="password" id="password" class="form-control" name="password"
                                   placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
                                   aria-describedby="password"/>
                            <span class="input-group-text cursor-pointer"><i class="ti ti-eye-off"></i></span>
                        </div>
                    </div>

                    <!--<div th:if="${#ctx.containsVariable('errorMessage')}" class="alert alert-danger" role="alert" th:text="#{page.login.bad.credentials.message}"></div>-->
                    <div th:if="${#ctx.containsVariable('errorMessage')}" class="alert alert-danger" role="alert" th:text="${errorMessage}"></div>

                    <!-- Token expiration message -->
                    <div th:if="${param.expired}" class="alert alert-warning" role="alert">
                        <i class="ti ti-clock-exclamation me-2"></i>
                        [[#{page.login.token.expired}]]
                    </div>

                    <!-- reCAPTCHA widget -->
                    <div class="mb-6" th:if="${showCaptcha}">
                        <div class="g-recaptcha" th:attr="data-sitekey=${recaptchaSiteKey}"></div>
                        <input type="hidden" name="recaptchaRequired" th:value="${showCaptcha}">
                    </div>

                    <div class="my-8">
                        <div class="d-flex justify-content-between">
                            <div class="form-check mb-0 ms-2">
                                <input class="form-check-input" type="checkbox" id="remember-me">
                                <label class="form-check-label" for="remember-me">
                                    [[#{page.login.rememberme}]]
                                </label>
                            </div>
                            <a th:href="@{/forgot-password}">
                                <p class="mb-0">[[#{page.login.forgotpassword}]]</p>
                            </a>
                        </div>
                    </div>
                    <button class="btn btn-primary d-grid w-100">
                        [[#{page.landing.menu.login}]]
                    </button>
                </form>
            </div>
        </div>
        <!-- /Login -->
    </div>
</div>

<!-- / Content -->

<!-- Core JS -->
<!-- build:js assets/vendor/js/core.js -->

<script th:src="@{/assets/vendor/libs/jquery/jquery.js}"></script>
<script th:src="@{/assets/vendor/libs/popper/popper.js}"></script>
<script th:src="@{/assets/vendor/js/bootstrap.js}"></script>
<script th:src="@{/assets/vendor/libs/node-waves/node-waves.js}"></script>
<script th:src="@{/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js}"></script>
<script th:src="@{/assets/vendor/libs/hammer/hammer.js}"></script>
<script th:src="@{/assets/vendor/libs/i18n/i18n.js}"></script>
<script th:src="@{/assets/vendor/libs/typeahead-js/typeahead.js}"></script>
<script th:src="@{/assets/vendor/js/menu.js}"></script>

<!-- endbuild -->

<!-- Vendors JS -->
<script th:src="@{/assets/vendor/libs/@form-validation/popular.js}"></script>
<script th:src="@{/assets/vendor/libs/@form-validation/bootstrap5.js}"></script>
<script th:src="@{/assets/vendor/libs/@form-validation/auto-focus.js}"></script>

<!-- Language Override JS -->
<script th:src="@{/js/language-override.js}"></script>
<!-- Main JS -->
<script th:src="@{/assets/js/main.js}"></script>


<!-- Page JS -->
<script th:src="@{/assets/js/pages-auth.js}"></script>

</body>

</html>
