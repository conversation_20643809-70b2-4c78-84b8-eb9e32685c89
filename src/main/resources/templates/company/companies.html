<!doctype html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <meta charset="UTF-8"/>
    <title th:text="#{company.title}">Companies Management</title>

    <style>
        /* Loading spinner animations */
        .htmx-settling .spinner-border {
            opacity: 0;
        }
        .spinner-border {
            transition: opacity 300ms ease-in;
        }

        /* Custom spinner styling */
        #loading-indicator .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
            border-width: 0.15em;
        }

        /* Smooth fade in/out for loading indicator */
        #loading-indicator {
            transition: opacity 200ms ease-in-out;
        }
    </style>
</head>
<body>

<th:block layout:fragment="optionalVendorsCSS">
    <!-- Vendor CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/select2/select2.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/%40form-validation/form-validation.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/animate-css/animate.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/sweetalert2/sweetalert2.css}"/>
</th:block>

<div layout:fragment="content" class="container-xxl flex-grow-1 container-p-y">
    <div class="row">
        <div class="col-md-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-6">
                <div>
                    <h4 class="mb-1" th:text="#{company.management.title}">Company Management</h4>
                    <p class="mb-0" th:text="#{company.management.subtitle}">Manage companies and their information</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button"
                            id="refreshButton"
                            class="btn btn-outline-secondary"
                            hx-get="/admin/companies/refresh"
                            hx-target="#companyContent"
                            hx-swap="outerHTML"
                            hx-indicator="#refreshSpinner"
                            hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter, #currentPageInput"
                            th:title="#{company.button.refresh}">
                        <i class="ti ti-refresh me-1"></i><span th:text="#{company.button.refresh}">Refresh</span>
                    </button>
                    <button type="button"
                            class="btn btn-primary"
                            hx-get="/admin/companies/form"
                            hx-target="#companyFormOffcanvas .offcanvas-body"
                            hx-swap="innerHTML"
                            hx-trigger="click"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#companyFormOffcanvas"
                            aria-controls="companyFormOffcanvas"
                            th:attr="data-title=#{company.button.create}">
                        <i class="ti ti-plus me-1"></i><span th:text="#{company.button.create}">Create Company</span>
                    </button>
                </div>
            </div>

            <!-- Company Content (Statistics + List) -->
            <div id="companyContent" th:replace="~{company/fragments/company-content :: companyContent}"></div>
        </div>
    </div>

    <!-- Company Form Offcanvas -->
    <div class="offcanvas offcanvas-end"
         id="companyFormOffcanvas"
         tabindex="-1"
         aria-labelledby="companyFormOffcanvasLabel"
         aria-describedby="companyFormOffcanvasDescription"
         data-bs-backdrop="true"
         data-bs-scroll="false"
         role="dialog"
         aria-modal="true">
        <div class="offcanvas-header">
            <div class="d-flex flex-column">
                <h5 class="offcanvas-title" id="companyFormOffcanvasLabel" th:text="#{company.form.title.create}">Company Form</h5>
                <p class="visually-hidden" id="companyFormOffcanvasDescription" th:text="#{company.form.description}">Form for adding or editing company information</p>
            </div>
            <button type="button"
                    class="btn-close"
                    data-bs-dismiss="offcanvas"
                    th:aria-label="#{modal.close}"></button>
        </div>
        <div class="offcanvas-body">
            <!-- Content will be loaded via HTMX -->
        </div>
    </div>

    <!-- Response container for modal messages -->
    <div id="response"></div>

    <!-- Company Users Offcanvas -->
    <div class="offcanvas offcanvas-end"
         id="companyUsersOffcanvas"
         tabindex="-1"
         aria-labelledby="companyUsersOffcanvasLabel"
         aria-describedby="companyUsersOffcanvasDescription"
         data-bs-backdrop="true"
         data-bs-scroll="false"
         role="dialog"
         aria-modal="true">
        <div class="offcanvas-header">
            <div class="d-flex flex-column">
                <h5 class="offcanvas-title" id="companyUsersOffcanvasLabel" th:text="#{company.users.title}">Company Users</h5>
                <p class="visually-hidden" id="companyUsersOffcanvasDescription" th:text="#{company.users.description}">View and manage users for this company</p>
            </div>
            <button type="button"
                    class="btn-close"
                    data-bs-dismiss="offcanvas"
                    th:aria-label="#{modal.close}"></button>
        </div>
        <div class="offcanvas-body">
            <!-- Content will be loaded via HTMX -->
        </div>
    </div>
</div>

<th:block layout:fragment="optionalVendorJS">
    <script th:src="@{/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js}"></script>
    <script th:src="@{/assets/vendor/libs/select2/select2.js}"></script>
</th:block>

<th:block layout:fragment="optionalPageJS">
    <script th:src="@{/js/htmx-extensions/bsSend.js}"></script>
    <script th:src="@{/js/modalResponse.js}"></script>

    <script th:inline="javascript">
        // Clear form function
        function clearCompanyForm() {
            // Close the offcanvas
            const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('companyFormOffcanvas'));
            if (offcanvas) {
                offcanvas.hide();
            }
        }

        // Global alert functions for company forms
        window.showCompanyFormAlert = function(type, title, message) {
            const alertBox = document.getElementById('companyFormAlert');
            const alertIcon = document.getElementById('companyFormAlertIcon');
            const alertTitle = document.getElementById('companyFormAlertTitle');
            const alertMessage = document.getElementById('companyFormAlertMessage');

            if (!alertBox) {
                return;
            }

            // Set content
            alertTitle.textContent = title;
            alertMessage.textContent = message;

            // Set icon and class based on type
            let iconClass = 'ti ti-alert-circle me-2';
            let alertClass = 'alert alert-danger alert-dismissible fade show';

            switch(type) {
                case 'success':
                    iconClass = 'ti ti-check-circle me-2';
                    alertClass = 'alert alert-success alert-dismissible fade show';
                    break;
                case 'warning':
                    iconClass = 'ti ti-alert-triangle me-2';
                    alertClass = 'alert alert-warning alert-dismissible fade show';
                    break;
                case 'info':
                    iconClass = 'ti ti-info-circle me-2';
                    alertClass = 'alert alert-info alert-dismissible fade show';
                    break;
                case 'error':
                default:
                    iconClass = 'ti ti-alert-circle me-2';
                    alertClass = 'alert alert-danger alert-dismissible fade show';
                    break;
            }

            alertIcon.className = iconClass;
            alertBox.className = alertClass;
            alertBox.style.display = 'block';

        };

        window.hideCompanyFormAlert = function() {
            const alertBox = document.getElementById('companyFormAlert');
            if (alertBox) {
                alertBox.style.display = 'none';
                alertBox.className = 'alert alert-dismissible fade';
            }
        };

        // Function to refresh company list while preserving current filters and pagination
        function refreshCompanyListWithCurrentState() {
            // Get current filter values
            const companyNameFilter = document.getElementById('companyNameFilter');
            const countryFilter = document.getElementById('countryFilter');
            const taxNumberFilter = document.getElementById('taxNumberFilter');
            const enabledFilter = document.getElementById('enabledFilter');
            const lockedFilter = document.getElementById('lockedFilter');
            const currentPageInput = document.getElementById('currentPageInput');

            // Build query parameters
            const params = new URLSearchParams();

            // Preserve current page
            const currentPage = currentPageInput ? currentPageInput.value : '1';
            params.append('page', currentPage);

            // Preserve all filter values
            if (companyNameFilter && companyNameFilter.value) params.append('companyName', companyNameFilter.value);
            if (countryFilter && countryFilter.value) params.append('country', countryFilter.value);
            if (taxNumberFilter && taxNumberFilter.value) params.append('taxNumber', taxNumberFilter.value);
            if (enabledFilter && enabledFilter.value) params.append('enabled', enabledFilter.value);
            if (lockedFilter && lockedFilter.value) params.append('locked', lockedFilter.value);

            // Show loading indicator
            const refreshSpinner = document.getElementById('refreshSpinner');
            if (refreshSpinner) {
                refreshSpinner.style.display = 'inline-block';
            }

            // Trigger refresh of the entire company content (statistics + list) with current state preserved
            htmx.ajax('GET', '/admin/companies/refresh?' + params.toString(), {
                target: '#companyContent',
                swap: 'outerHTML'
            }).then(function() {
                // Hide loading indicator after refresh completes
                const refreshSpinner = document.getElementById('refreshSpinner');
                if (refreshSpinner) {
                    refreshSpinner.style.display = 'none';
                }
            }).catch(function(error) {
                // Hide loading indicator on error
                const refreshSpinner = document.getElementById('refreshSpinner');
                if (refreshSpinner) {
                    refreshSpinner.style.display = 'none';
                }
            });
        }

        // Make the function globally available for HTMX inline handlers
        window.refreshCompanyListWithCurrentState = refreshCompanyListWithCurrentState;

        // Global function to initialize Select2 for country dropdowns
        window.initializeSelect2 = function() {
            // Initialize all Select2 elements that aren't already initialized
            $('.select2').each(function() {
                const $element = $(this);

                // Skip if already initialized
                if ($element.hasClass('select2-hidden-accessible')) {
                    return;
                }

                // Get the parent container for dropdown positioning
                let dropdownParent = $element.closest('.offcanvas');
                if (dropdownParent.length === 0) {
                    dropdownParent = $element.closest('.modal');
                }
                if (dropdownParent.length === 0) {
                    dropdownParent = $('body');
                }

                try {
                    // Initialize Select2 with search functionality
                    $element.select2({
                        placeholder: $element.data('placeholder') || /*[[#{common.search.and.select}]]*/ 'Search and select...',
                        allowClear: true,
                        width: '100%',
                        dropdownParent: dropdownParent,
                        minimumInputLength: 0,
                        minimumResultsForSearch: 0,
                        closeOnSelect: true,
                        tags: false,
                        escapeMarkup: function(markup) {
                            return markup;
                        },
                        matcher: function(params, data) {
                            // If there are no search terms, return all data
                            if (!params.term || $.trim(params.term) === '') {
                                return data;
                            }

                            // Do not display the item if there is no 'text' property
                            if (typeof data.text === 'undefined') {
                                return null;
                            }

                            // Case-insensitive search
                            if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                                return data;
                            }

                            // Return `null` if the term should not be displayed
                            return null;
                        }
                    });

                    // Fix accessibility issue with aria-hidden
                    // Remove aria-hidden from the container and use inert attribute instead
                    setTimeout(function() {
                        const select2Container = $element.next('.select2-container');
                        if (select2Container.length) {
                            select2Container.removeAttr('aria-hidden');

                            // Find any elements with aria-hidden that might contain focusable elements
                            select2Container.find('[aria-hidden="true"]').each(function() {
                                const $this = $(this);
                                // If this element contains focusable elements and is not meant to be interactive
                                if ($this.find('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])').length) {
                                    $this.removeAttr('aria-hidden');
                                    // Use inert attribute if supported
                                    if ('inert' in document.createElement('div')) {
                                        $this.attr('inert', '');
                                    }
                                }
                            });
                        }
                    }, 100);
                } catch (error) {
                    console.error('Error initializing Select2:', error);
                }
            });
        };

        // Initialize Select2 on page load
        $(document).ready(function() {
            if (typeof $.fn.select2 === 'undefined') {
                console.error('Select2 is not loaded! Check if select2.js is included.');
                return;
            }

            initializeSelect2();
        });



        // Clear filters is now handled by HTMX

        // Handle HTMX responses for company forms
        document.body.addEventListener('htmx:afterRequest', function(event) {
            const form = event.target;
            if (form && form.id === 'companyFormElement') {
                handleCompanyFormResponse(event);
            }
        });

        // Handle HTMX responses for company forms
        function handleCompanyFormResponse(event) {
            const xhr = event.detail.xhr;
            const form = event.target;


            if (xhr.status === 200) {
                // Check if the response is JSON before trying to parse it
                const contentType = xhr.getResponseHeader('Content-Type');
                if (contentType && contentType.includes('application/json')) {
                    // Success response - try to parse JSON
                    try {
                        const responseObj = JSON.parse(xhr.responseText);
                        console.log('Success response object:', responseObj);

                        if (responseObj.icon && responseObj.icon.includes('success')) {
                            // Success - close offcanvas and refresh list
                            console.log('Company operation successful, closing offcanvas and refreshing list');

                            // Hide any existing alert
                            hideCompanyFormAlert();

                            // Close the offcanvas with a slight delay to ensure smooth UX
                            setTimeout(() => {
                                const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('companyFormOffcanvas'));
                                if (offcanvas) {
                                    offcanvas.hide();
                                }

                                // Show loading indicator if available
                                const refreshSpinner = document.getElementById('refreshSpinner');
                                if (refreshSpinner) {
                                    refreshSpinner.style.display = 'inline-block';
                                }

                                // Refresh the company content with current state preserved
                                refreshCompanyListWithCurrentState();
                            }, 500);
                        } else {
                            // Error response - show alert box
                            console.log('Error response object:', responseObj);
                            showCompanyFormAlert('error', responseObj.title || /*[[#{alert.error.title}]]*/ 'Error', responseObj.text || /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request');
                        }
                    } catch (e) {
                        // If response is not valid JSON, show generic error alert box
                        console.error('Error parsing response:', e);
                        showCompanyFormAlert('error', /*[[#{alert.error.title}]]*/ 'Error', /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request');
                    }
                } else {
                    // Response is not JSON, assume it's HTML and handle accordingly
                    console.log('Received HTML response');

                    // Hide any existing alert
                    hideCompanyFormAlert();

                    // If this is a successful form submission, close the offcanvas and refresh
                    if (xhr.responseText.includes('success')) {
                        setTimeout(() => {
                            const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('companyFormOffcanvas'));
                            if (offcanvas) {
                                offcanvas.hide();
                            }

                            refreshCompanyListWithCurrentState();
                        }, 500);
                    }
                }
            } else if (xhr.status === 400) {
                // Handle validation errors - try to parse and show field-specific errors
                try {
                    const responseText = xhr.responseText;
                    console.log('400 response text:', responseText);

                    const responseObj = JSON.parse(responseText);
                    console.log('400 response object:', responseObj);

                    if (responseObj.fieldErrors && Object.keys(responseObj.fieldErrors).length > 0) {
                        // Handle field-specific errors
                        handleCompanyFieldErrors(responseObj.fieldErrors);
                    } else {
                        // Show general error alert box
                        let alertType = 'warning';
                        if (responseObj.text && (responseObj.text.includes('already exists') || responseObj.text.includes('já existe'))) {
                            alertType = 'error'; // Company already exists should be an error, not warning
                        }

                        console.log('Showing 400 error alert:', alertType, responseObj.title, responseObj.text);
                        showCompanyFormAlert(alertType, responseObj.title || /*[[#{alert.error.title}]]*/ 'Error', responseObj.text || /*[[#{alert.error.validation}]]*/ 'Please correct the validation errors and try again');
                    }
                } catch (e) {
                    // Show generic error alert box
                    showCompanyFormAlert('warning', /*[[#{alert.warning.title}]]*/ 'Warning', /*[[#{alert.error.validation}]]*/ 'Please correct the validation errors and try again');
                }
            } else {
                // Show error alert box for other non-200 responses
                showCompanyFormAlert('error', /*[[#{alert.error.title}]]*/ 'Error', /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request');
            }
        }

        // Handle field-specific validation errors
        function handleCompanyFieldErrors(fieldErrors) {
            console.log('Handling company field errors:', fieldErrors);

            let hasFieldErrors = false;

            // Clear existing field errors first
            document.querySelectorAll('#companyFormOffcanvas .is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
            document.querySelectorAll('#companyFormOffcanvas .invalid-feedback').forEach(el => {
                el.innerHTML = '';
            });

            // Apply field-specific errors
            Object.keys(fieldErrors).forEach(fieldName => {
                const field = document.querySelector(`#companyFormOffcanvas [name="${fieldName}"]`);
                const feedback = document.querySelector(`#companyFormOffcanvas [name="${fieldName}"] + .invalid-feedback`);

                if (field) {
                    field.classList.add('is-invalid');
                    if (feedback) {
                        feedback.innerHTML = fieldErrors[fieldName];
                    }
                    hasFieldErrors = true;
                    console.log('Applied error to field:', fieldName, fieldErrors[fieldName]);
                }
            });

            // Show general alert if no field-specific errors were applied
            const fieldsWithErrors = Object.keys(fieldErrors).filter(fieldName => {
                return document.querySelector(`[name="${fieldName}"]`);
            });

            if (!hasFieldErrors) {
                const errorMessage = Object.values(fieldErrors).join(', ');
                showCompanyFormAlert('warning', /*[[#{alert.warning.title}]]*/ 'Warning', errorMessage);
            }
        }

        // HTMX Error Handling for Company Forms (only for 500+ errors, 400 errors are handled by handleCompanyFormResponse)
        document.body.addEventListener('htmx:responseError', function(event) {
            const form = event.target;
            if (form && form.id === 'companyFormElement') {
                console.log('HTMX response error for company form:', event.detail);

                // Only handle 500+ errors here, 400-level errors are handled in handleCompanyFormResponse
                if (event.detail.xhr.status >= 500) {
                    let errorTitle = /*[[#{alert.error.title}]]*/ 'Server Error';
                    let errorMessage = /*[[#{alert.error.server}]]*/ 'A server error occurred while processing your request';

                    // Try to parse error response
                    try {
                        if (event.detail.xhr && event.detail.xhr.responseText) {
                            // Check if the response is valid JSON before trying to parse it
                            const responseText = event.detail.xhr.responseText;
                            if (isValidJSON(responseText)) {
                                const response = JSON.parse(responseText);
                                if (response.title) errorTitle = response.title;
                                if (response.text || response.message) errorMessage = response.text || response.message;
                            }
                        } else {
                            // If no response text, use status-based message
                            const responseText = event.detail.xhr.responseText || '';
                            errorMessage = responseText.substring(0, 200) + (responseText.length > 200 ? '...' : '');
                        }
                    } catch (e) {
                        console.log('Could not parse error response:', e);
                        // Use default error message
                        const responseText = event.detail.xhr.responseText || '';
                        if (responseText.trim()) {
                            errorMessage = responseText.substring(0, 200) + (responseText.length > 200 ? '...' : '');
                        }
                    }

                    console.log('Showing alert with:', errorTitle, errorMessage);
                    showCompanyFormAlert('error', errorTitle, errorMessage);
                } else {
                    console.log('400-level error, will be handled by handleCompanyFormResponse');
                }
            }
        });

        // HTMX Network Error Handling
        document.body.addEventListener('htmx:sendError', function(event) {
            const form = event.target;
            if (form && form.id === 'companyFormElement') {
                console.log('HTMX send error for company form:', event.detail);
                showCompanyFormAlert('error', /*[[#{alert.error.title}]]*/ 'Error', /*[[#{alert.error.network}]]*/ 'Unable to connect to the server. Please check your internet connection');
            }
        });

        // HTMX Timeout Handling
        document.body.addEventListener('htmx:timeout', function(event) {
            const form = event.target;
            if (form && form.id === 'companyFormElement') {
                console.log('HTMX timeout for company form:', event.detail);
                showCompanyFormAlert('warning', /*[[#{alert.warning.title}]]*/ 'Warning', /*[[#{alert.error.timeout}]]*/ 'The request is taking longer than expected. Please try again');
            }
        });

        // Initialize form validation when forms are loaded via HTMX
        document.body.addEventListener('htmx:afterSwap', function(event) {
            // More robust approach: check if we have any uninitialized Select2 elements
            setTimeout(function() {
                // Check if there are any Select2 elements that need initialization
                const uninitializedSelect2 = $('.select2:not(.select2-hidden-accessible)');
                if (uninitializedSelect2.length > 0) {
                    console.log('Found uninitialized Select2 elements after HTMX swap, initializing...');
                    initializeSelect2();
                }

                // Also hide any existing alert when form is loaded
                if (document.querySelector('#companyFormOffcanvas .offcanvas-body')) {
                    hideCompanyFormAlert();
                }
            }, 200);
        });

        // Also handle when offcanvas is shown
        document.addEventListener('DOMContentLoaded', function() {
            const companyOffcanvas = document.getElementById('companyFormOffcanvas');
            if (companyOffcanvas) {
                companyOffcanvas.addEventListener('shown.bs.offcanvas', function() {
                    setTimeout(function() {
                        initializeSelect2();
                    }, 150);
                });
            }
        });

        // Delete confirmation is handled in the company-list fragment

        // Show loading indicator only for company-related HTMX requests
        document.body.addEventListener('htmx:beforeRequest', function(event) {
            // Add null checks
            if (event && event.detail && event.detail.pathInfo && event.detail.pathInfo.requestPath) {
                const requestPath = event.detail.pathInfo.requestPath;
                if (requestPath.includes('/admin/companies') || requestPath.includes('/companies')) {
                    const loadingIndicator = document.getElementById('loading-indicator');
                    if (loadingIndicator) {
                        loadingIndicator.classList.remove('d-none');
                    }
                }
            }
        });

        document.body.addEventListener('htmx:afterRequest', function(event) {
            // Add null checks
            if (event && event.detail && event.detail.pathInfo && event.detail.pathInfo.requestPath) {
                const requestPath = event.detail.pathInfo.requestPath;
                if (requestPath.includes('/admin/companies') || requestPath.includes('/companies')) {
                    const loadingIndicator = document.getElementById('loading-indicator');
                    if (loadingIndicator) {
                        loadingIndicator.classList.add('d-none');
                    }
                }
            }
        });

        // Clear offcanvas content when it's hidden
        document.getElementById('companyFormOffcanvas').addEventListener('hidden.bs.offcanvas', function() {
            // Clear the offcanvas body content
            const offcanvasBody = this.querySelector('.offcanvas-body');
            if (offcanvasBody) {
                offcanvasBody.innerHTML = '<!-- Content will be loaded via HTMX -->';
            }

            // Reset the title
            const titleElement = document.getElementById('companyFormOffcanvasLabel');
            if (titleElement) {
                titleElement.textContent = /*[[#{company.form.title.create}]]*/ 'Company Form';
            }

            // Clear any response messages
            const responseContainer = document.getElementById('response');
            if (responseContainer) {
                responseContainer.innerHTML = '';
            }

            // Clear any alert messages
            hideCompanyFormAlert();
        });

        // Re-initialize form validation for dynamically loaded forms
        document.body.addEventListener('htmx:afterSwap', function(event) {
            // Add null checks and only process company form related swaps
            if (event && event.detail && event.detail.target && event.detail.target.closest) {
                if (event.detail.target.closest('#companyFormOffcanvas')) {
                    // Re-initialize bsSend validation for dynamically loaded forms
                    const forms = document.querySelectorAll('#companyFormOffcanvas .needs-validation');
                    forms.forEach(function (form) {
                        // Remove existing event listeners to avoid duplicates
                        const newForm = form.cloneNode(true);
                        form.parentNode.replaceChild(newForm, form);

                        // Add the validation event listener
                        newForm.addEventListener('submit', function (event) {
                            if (newForm.checkValidity()) {
                                // trigger custom event hx-trigger="bs-send"
                                htmx.trigger(newForm, "bsSend");
                                console.log('bsSend triggered for dynamically loaded company form');
                            }

                            console.log('Company form validation prevented default submission');
                            event.preventDefault();
                            event.stopPropagation();

                            newForm.classList.add('was-validated');
                        }, false);
                    });
                }
            }
        });
    </script>
</th:block>

</body>
</html>
