<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Company List Fragment</title>
</head>
<body>

<div th:fragment="companyList"
     id="companyList"
     hx-trigger="refresh from:body"
     hx-get="/admin/companies/list"
     hx-swap="outerHTML"
     th:hx-vals="|{'page': '${currentPage}', 'companyName': '${companyNameFilter}', 'country': '${countryFilter}', 'taxNumber': '${taxNumberFilter}', 'enabled': '${enabledFilter}', 'locked': '${lockedFilter}'}|">
    <table class="table table-hover">
        <thead>
            <!-- Filter Row -->
            <tr>
                <th class="text-truncate align-middle" style="width: 25%;">
                    <!-- Company Name Filter -->
                    <input type="text"
                           id="companyNameFilter"
                           name="companyName"
                           class="form-control form-control-sm"
                           th:placeholder="#{company.filter.companyName}"
                           th:value="${companyNameFilter}"
                           hx-get="/admin/companies/list"
                           hx-target="#companyList"
                           hx-swap="outerHTML"
                           hx-trigger="change, keyup delay:500ms"
                           hx-include="#countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter"
                           hx-vals='{"page": "1"}'
                           hx-indicator="#refreshSpinner">
                </th>
                <th class="text-truncate align-middle" style="width: 20%;" th:text="#{company.field.address}">Address</th>
                <th class="text-truncate align-middle" style="width: 12%;">
                    <!-- Country Filter -->
                    <select id="countryFilter"
                            name="country"
                            class="form-select form-select-sm"
                            hx-get="/admin/companies/list"
                            hx-target="#companyList"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="#companyNameFilter, #taxNumberFilter, #enabledFilter, #lockedFilter"
                            hx-vals='{"page": "1"}'
                            hx-indicator="#refreshSpinner">
                        <option value="" th:text="#{company.filter.country.all}">All Countries</option>
                        <option th:each="country : ${countries}"
                                th:value="${country}"
                                th:text="${country}"
                                th:selected="${country == countryFilter}">Country</option>
                    </select>
                </th>
                <th class="text-truncate align-middle" style="width: 12%;">
                    <!-- TAX Number Filter -->
                    <input type="text"
                           id="taxNumberFilter"
                           name="taxNumber"
                           class="form-control form-control-sm"
                           th:placeholder="#{company.filter.taxNumber}"
                           th:value="${taxNumberFilter}"
                           hx-get="/admin/companies/list"
                           hx-target="#companyList"
                           hx-swap="outerHTML"
                           hx-trigger="change, keyup delay:500ms"
                           hx-include="#companyNameFilter, #countryFilter, #enabledFilter, #lockedFilter"
                           hx-vals='{"page": "1"}'
                           hx-indicator="#refreshSpinner">
                </th>
                <th class="text-truncate align-middle" style="width: 8%;" th:text="#{company.field.domainName}">Domain</th>
                <th class="text-truncate align-middle" style="width: 10%;">
                    <!-- Status Filter -->
                    <select id="enabledFilter"
                            name="enabled"
                            class="form-select form-select-sm"
                            hx-get="/admin/companies/list"
                            hx-target="#companyList"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #lockedFilter"
                            hx-vals='{"page": "1"}'
                            hx-indicator="#refreshSpinner">
                        <option value="" th:text="#{company.filter.status.all}">All Status</option>
                        <option value="true" th:selected="${enabledFilter != null && enabledFilter}" th:text="#{company.status.enabled}">Enabled</option>
                        <option value="false" th:selected="${enabledFilter != null && !enabledFilter}" th:text="#{company.status.disabled}">Disabled</option>
                    </select>
                </th>
                <th class="text-truncate align-middle" style="width: 10%;">
                    <!-- Lock Status Filter -->
                    <select id="lockedFilter"
                            name="locked"
                            class="form-select form-select-sm"
                            hx-get="/admin/companies/list"
                            hx-target="#companyList"
                            hx-swap="outerHTML"
                            hx-trigger="change"
                            hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter"
                            hx-vals='{"page": "1"}'
                            hx-indicator="#refreshSpinner">
                        <option value="" th:text="#{company.filter.lock.all}">All Locks</option>
                        <option value="false" th:selected="${lockedFilter != null && !lockedFilter}" th:text="#{company.status.unlocked}">Unlocked</option>
                        <option value="true" th:selected="${lockedFilter != null && lockedFilter}" th:text="#{company.status.locked}">Locked</option>
                    </select>
                </th>
                <th class="text-truncate align-middle" style="width: 6%;">
                    <!-- Clear Filters Button -->
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary"
                            hx-get="/admin/companies/clearFilters"
                            hx-target="#companyList"
                            hx-swap="outerHTML"
                            hx-indicator="#refreshSpinner"
                            th:title="#{company.button.clear.filters}">
                        <i class="ti ti-filter-off"></i>
                    </button>
                </th>
            </tr>
        </thead>
        <tbody id="companyListBody">
            <tr th:if="${companies.empty}">
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="ti ti-building-store ti-lg mb-2"></i>
                    <div th:text="#{company.list.empty}">No companies found</div>
                </td>
            </tr>
            <tr th:each="company : ${companies.content}" class="company-row">
                <td>
                    <div class="fw-medium" th:text="${company.companyName}">Company Name</div>
                </td>
                <td>
                    <div class="text-truncate" th:text="${company.address}" th:title="${company.address}">Address</div>
                </td>
                <td th:text="${company.country}">Country</td>
                <td>
                    <span class="badge bg-label-info" th:text="${company.taxNumber}">TAX123</span>
                </td>
                <td th:text="${company.domainName ?: '-'}">domain.com</td>
                <td>
                    <span th:if="${company.enabled}" class="badge bg-label-success" th:text="#{company.status.enabled}">Enabled</span>
                    <span th:unless="${company.enabled}" class="badge bg-label-secondary" th:text="#{company.status.disabled}">Disabled</span>
                </td>
                <td>
                    <span th:if="${company.locked}" class="badge bg-label-danger">
                        <i class="ti ti-lock ti-xs"></i> <span th:text="#{company.status.locked}">Locked</span>
                    </span>
                    <span th:unless="${company.locked}" class="badge bg-label-success">
                        <i class="ti ti-lock-open ti-xs"></i> <span th:text="#{company.status.unlocked}">Unlocked</span>
                    </span>
                </td>
                <td>
                    <div class="dropdown">
                        <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                            <i class="ti ti-dots-vertical"></i>
                        </button>
                        <div class="dropdown-menu">
                            <!-- Edit Company -->
                            <a class="dropdown-item"
                               href="javascript:void(0);"
                               th:hx-get="@{/admin/companies/form(id=${company.id})}"
                               hx-target="#companyFormOffcanvas .offcanvas-body"
                               hx-swap="innerHTML"
                               hx-trigger="click"
                               data-bs-toggle="offcanvas"
                               data-bs-target="#companyFormOffcanvas"
                               aria-controls="companyFormOffcanvas"
                               th:attr="data-company-id=${company.id}, data-title=#{company.button.edit}"
                               th:title="#{company.button.edit}">
                                <i class="ti ti-pencil me-1"></i> <span th:text="#{company.button.edit}">Edit</span>
                            </a>

                            <!-- Enable/Disable Company -->
                            <a th:if="${company.enabled}"
                               class="dropdown-item text-warning"
                               href="javascript:void(0);"
                               th:hx-put="@{/admin/companies/disable/{id}(id=${company.id})}"
                               hx-swap="none"
                               th:hx-confirm="#{company.disable.confirm}"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshCompanyListWithCurrentState(); }">
                                <i class="ti ti-ban me-1"></i> <span th:text="#{company.button.disable}">Disable</span>
                            </a>
                            <a th:if="${!company.enabled}"
                               class="dropdown-item text-success"
                               href="javascript:void(0);"
                               th:hx-put="@{/admin/companies/enable/{id}(id=${company.id})}"
                               hx-swap="none"
                               th:hx-confirm="#{company.enable.confirm}"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshCompanyListWithCurrentState(); }">
                                <i class="ti ti-check me-1"></i> <span th:text="#{company.button.enable}">Enable</span>
                            </a>

                            <!-- Lock/Unlock Company -->
                            <a th:if="${!company.locked}"
                               class="dropdown-item text-danger"
                               href="javascript:void(0);"
                               th:hx-put="@{/admin/companies/lock/{id}(id=${company.id})}"
                               hx-swap="none"
                               th:hx-confirm="#{company.lock.confirm}"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshCompanyListWithCurrentState(); }">
                                <i class="ti ti-lock me-1"></i> <span th:text="#{company.button.lock}">Lock</span>
                            </a>
                            <a th:if="${company.locked}"
                               class="dropdown-item text-info"
                               href="javascript:void(0);"
                               th:hx-put="@{/admin/companies/unlock/{id}(id=${company.id})}"
                               hx-swap="none"
                               th:hx-confirm="#{company.unlock.confirm}"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshCompanyListWithCurrentState(); }">
                                <i class="ti ti-lock-open me-1"></i> <span th:text="#{company.button.unlock}">Unlock</span>
                            </a>

                            <div class="dropdown-divider"></div>

                            <!-- View Users -->
                            <a class="dropdown-item"
                               href="javascript:void(0);"
                               th:hx-get="@{/admin/companies/{id}/users(id=${company.id})}"
                               hx-target="#companyUsersOffcanvas .offcanvas-body"
                               hx-swap="innerHTML"
                               hx-trigger="click"
                               data-bs-toggle="offcanvas"
                               data-bs-target="#companyUsersOffcanvas"
                               aria-controls="companyUsersOffcanvas"
                               th:attr="data-company-id=${company.id}, data-company-name=${company.companyName}"
                               th:title="#{company.button.view.users}">
                                <i class="ti ti-users me-1"></i> <span th:text="#{company.button.view.users}">View Users</span>
                            </a>

                            <div class="dropdown-divider"></div>

                            <!-- Delete Company -->
                            <a class="dropdown-item text-danger"
                               href="javascript:void(0);"
                               th:hx-delete="@{/admin/companies/delete/{id}(id=${company.id})}"
                               th:hx-confirm="#{company.delete.confirm}"
                               hx-swap="none"
                               hx-trigger="click"
                               hx-on::after-request="if(event.detail.xhr.status === 200) { refreshCompanyListWithCurrentState(); }">
                                <i class="ti ti-trash me-1"></i> <span th:text="#{company.button.delete}">Delete</span>
                            </a>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>

    <!-- Pagination -->
    <div th:if="${!companies.empty}" class="d-flex justify-content-between align-items-center mt-3 px-5">
        <div class="text-muted">
            <span th:text="#{table.pagination.showing.from}">Showing from</span>
            <span th:text="${companies.number * companies.size + 1}">1</span>
            <span th:text="#{table.pagination.to}">to</span>
            <span th:text="${companies.number * companies.size + companies.numberOfElements}">10</span>
            <span th:text="#{table.pagination.of.total}">of total</span>
            <span th:text="${companies.totalElements}">100</span>
            <span th:text="#{table.pagination.entries}">entries</span>
        </div>

        <nav th:aria-label="#{company.pagination.label}" th:if="${companies.totalPages > 1}">
            <ul class="pagination pagination-rounded pagination-sm mb-0">
                <!-- First Page Button -->
                <li class="paginate_button page-item first" th:classappend="${companies.first} ? 'disabled' : ''">
                    <a href="#"
                       class="page-link"
                       th:hx-get="@{/admin/companies/list(page=1)}"
                       hx-target="#companyList"
                       hx-swap="outerHTML"
                       hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter"
                       hx-indicator="#refreshSpinner">
                        <i class="ti ti-chevrons-left ti-xs"></i>
                    </a>
                </li>

                <!-- Previous Button -->
                <li class="paginate_button page-item previous" th:classappend="${!companies.hasPrevious()} ? 'disabled' : ''">
                    <a href="#"
                       class="page-link"
                       th:hx-get="@{/admin/companies/list(page=${companies.number})}"
                       hx-target="#companyList"
                       hx-swap="outerHTML"
                       hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter"
                       hx-indicator="#refreshSpinner">
                        <i class="ti ti-chevron-left ti-xs"></i>
                    </a>
                </li>

                <!-- If total pages <= 5, show all pages -->
                <th:block th:if="${companies.totalPages <= 5}">
                    <th:block th:each="index : ${#numbers.sequence(1, companies.totalPages, 1)}">
                        <li class="paginate_button page-item" th:classappend="${index == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="${index}"
                               th:hx-get="@{/admin/companies/list(page=${index})}"
                               hx-target="#companyList"
                               hx-swap="outerHTML"
                               hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter">
                            </a>
                        </li>
                    </th:block>
                </th:block>

                <!-- If total pages > 5, use smart pagination -->
                <th:block th:if="${companies.totalPages > 5}">
                    <!-- Case 1: currentPage in first 5 pages - Show first 5 pages, ellipsis, last page -->
                    <th:block th:if="${currentPage <= 5}">
                        <!-- First 5 pages -->
                        <th:block th:each="index : ${#numbers.sequence(1, 5, 1)}">
                            <li class="paginate_button page-item" th:classappend="${index == currentPage} ? 'active' : ''">
                                <a href="#"
                                   class="page-link"
                                   th:text="${index}"
                                   th:hx-get="@{/admin/companies/list(page=${index})}"
                                   hx-target="#companyList"
                                   hx-swap="outerHTML"
                                   hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter">
                                </a>
                            </li>
                        </th:block>

                        <!-- Ellipsis if needed -->
                        <li class="paginate_button page-item disabled" th:if="${companies.totalPages > 6}">
                            <span class="page-link">...</span>
                        </li>

                        <!-- Last page -->
                        <li class="paginate_button page-item" th:if="${companies.totalPages > 5}" th:classappend="${companies.totalPages == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="${companies.totalPages}"
                               th:hx-get="@{/admin/companies/list(page=${companies.totalPages})}"
                               hx-target="#companyList"
                               hx-swap="outerHTML"
                               hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter">
                            </a>
                        </li>
                    </th:block>

                    <!-- Case 2: currentPage in middle segment - Show page 1, ellipsis, pages around current, ellipsis, last page -->
                    <th:block th:if="${currentPage > 5 && currentPage < companies.totalPages - 4}">
                        <!-- First page -->
                        <li class="paginate_button page-item" th:classappend="${1 == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="1"
                               th:hx-get="@{/admin/companies/list(page=1)}"
                               hx-target="#companyList"
                               hx-swap="outerHTML"
                               hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter">
                            </a>
                        </li>

                        <!-- Ellipsis -->
                        <li class="paginate_button page-item disabled">
                            <span class="page-link">...</span>
                        </li>

                        <!-- Pages around current page (current-1, current, current+1) -->
                        <th:block th:each="index : ${#numbers.sequence(currentPage - 1, currentPage + 1, 1)}">
                            <li class="paginate_button page-item" th:classappend="${index == currentPage} ? 'active' : ''">
                                <a href="#"
                                   class="page-link"
                                   th:text="${index}"
                                   th:hx-get="@{/admin/companies/list(page=${index})}"
                                   hx-target="#companyList"
                                   hx-swap="outerHTML"
                                   hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter">
                                </a>
                            </li>
                        </th:block>

                        <!-- Ellipsis -->
                        <li class="paginate_button page-item disabled">
                            <span class="page-link">...</span>
                        </li>

                        <!-- Last page -->
                        <li class="paginate_button page-item" th:classappend="${companies.totalPages == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="${companies.totalPages}"
                               th:hx-get="@{/admin/companies/list(page=${companies.totalPages})}"
                               hx-target="#companyList"
                               hx-swap="outerHTML"
                               hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter">
                            </a>
                        </li>
                    </th:block>

                    <!-- Case 3: currentPage in last 5 pages - Show page 1, ellipsis, last 5 pages -->
                    <th:block th:if="${currentPage >= companies.totalPages - 4}">
                        <!-- First page -->
                        <li class="paginate_button page-item" th:classappend="${1 == currentPage} ? 'active' : ''">
                            <a href="#"
                               class="page-link"
                               th:text="1"
                               th:hx-get="@{/admin/companies/list(page=1)}"
                               hx-target="#companyList"
                               hx-swap="outerHTML"
                               hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter">
                            </a>
                        </li>

                        <!-- Ellipsis if needed -->
                        <li class="paginate_button page-item disabled" th:if="${companies.totalPages > 6}">
                            <span class="page-link">...</span>
                        </li>

                        <!-- Last 5 pages -->
                        <th:block th:each="index : ${#numbers.sequence(companies.totalPages - 4, companies.totalPages, 1)}">
                            <li class="paginate_button page-item" th:classappend="${index == currentPage} ? 'active' : ''">
                                <a href="#"
                                   class="page-link"
                                   th:text="${index}"
                                   th:hx-get="@{/admin/companies/list(page=${index})}"
                                   hx-target="#companyList"
                                   hx-swap="outerHTML"
                                   hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter">
                                </a>
                            </li>
                        </th:block>
                    </th:block>
                </th:block>

                <!-- Next Button -->
                <li class="paginate_button page-item next" th:classappend="${!companies.hasNext()} ? 'disabled' : ''">
                    <a href="#"
                       class="page-link"
                       th:hx-get="@{/admin/companies/list(page=${companies.number + 2})}"
                       hx-target="#companyList"
                       hx-swap="outerHTML"
                       hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter"
                       hx-indicator="#refreshSpinner">
                        <i class="ti ti-chevron-right ti-xs"></i>
                    </a>
                </li>

                <!-- Last Page Button -->
                <li class="paginate_button page-item last" th:classappend="${companies.last} ? 'disabled' : ''">
                    <a href="#"
                       class="page-link"
                       th:hx-get="@{/admin/companies/list(page=${companies.totalPages})}"
                       hx-target="#companyList"
                       hx-swap="outerHTML"
                       hx-include="#companyNameFilter, #countryFilter, #taxNumberFilter, #enabledFilter, #lockedFilter"
                       hx-indicator="#refreshSpinner">
                        <i class="ti ti-chevrons-right ti-xs"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</div>

<!-- Delete confirmation is now handled by HTMX hx-confirm attribute -->

</body>
</html>
