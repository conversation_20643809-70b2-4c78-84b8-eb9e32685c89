<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<body>

<div th:fragment="companyContent"
     id="companyContent"
     hx-trigger="refresh from:body"
     hx-get="/admin/companies/refresh"
     hx-swap="outerHTML"
     hx-indicator="#refreshSpinner">
    <!-- Company Statistics Cards -->
    <div class="row mb-6">
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{company.stats.total}">Total Companies</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${totalCompanies}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{company.stats.total.description}">All registered companies</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-primary">
                                <i class="ti ti-building-store ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{company.stats.enabled}">Enabled Companies</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${enabledCompanies}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{company.stats.enabled.description}">Active companies</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-success">
                                <i class="ti ti-check ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{company.stats.disabled}">Disabled Companies</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${disabledCompanies}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{company.stats.disabled.description}">Inactive companies</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-secondary">
                                <i class="ti ti-ban ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading" th:text="#{company.stats.locked}">Locked Companies</span>
                            <div class="d-flex align-items-center my-1">
                                <h4 class="mb-0 me-2" th:text="${lockedCompanies}">0</h4>
                            </div>
                            <small class="mb-0" th:text="#{company.stats.locked.description}">Temporarily locked companies</small>
                        </div>
                        <div class="avatar">
                            <span class="avatar-initial rounded bg-label-warning">
                                <i class="ti ti-lock ti-26px"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Company List Card -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0" th:text="#{company.list.title}">Companies</h5>
                <!-- Loading Spinner -->
                <div id="refreshSpinner" class="htmx-indicator">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- Company List Fragment -->
            <div th:replace="~{company/fragments/company-list :: companyList}"></div>
        </div>
    </div>
</div>

</body>
</html>
