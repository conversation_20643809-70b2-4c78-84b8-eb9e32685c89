<!DOCTYPE html>
<html th:lang="${#locale.language}" class="light-style layout-wide  customizer-hide "
      dir="ltr"
      data-theme="theme-default"
      data-assets-path="/assets/"
      data-template="vertical-menu-template" data-style="light"
      xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="utf-8"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0"/>

    <title>Jobify - Admin</title>


    <meta name="description" content="Start your development with a Dashboard for Bootstrap 5"/>
    <meta name="keywords" content="dashboard, bootstrap 5 dashboard, bootstrap 5 design, bootstrap 5">
    <!-- Canonical SEO -->
    <link rel="canonical" href="https://1.envato.market/vuexy_admin">

    <!-- Favicon -->
    <link type="image/x-icon" rel="icon" th:href="@{/assets/img/favicon/favicon.ico}"/>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&ampdisplay=swap" rel="stylesheet">

    <!-- Icons -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/fontawesome.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/tabler-icons.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/fonts/flag-icons.css}"/>

    <!-- Core CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/rtl/core.css}" class="template-customizer-core-css"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/rtl/theme-default.css}" class="template-customizer-theme-css"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/css/demo.css}"/>

    <!-- Vendors CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/node-waves/node-waves.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css}"/>
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/libs/typeahead-js/typeahead.css}"/>

    <!-- Page CSS -->
    <link type="text/css" rel="stylesheet" th:href="@{/assets/vendor/css/pages/page-misc.css}"/>

    <!-- Helpers -->
    <script th:src="@{/assets/vendor/js/helpers.js}"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->

    <!--? Template customizer: To hide customizer set displayCustomizer value false in config.js.  -->
    <script th:src="@{/assets/vendor/js/template-customizer.js}"></script>

    <!--? Config:  Mandatory theme config file contain global vars & default theme options, Set your preferred theme option in this file.  -->
    <script th:src="@{/assets/js/config.js}"></script>

</head>

<body>

<!-- Error -->
<div class="container-xxl container-p-y">
    <div class="misc-wrapper">
        <h1 class="mb-2 mx-2" style="line-height: 6rem;font-size: 6rem;">404</h1>
        <h4 class="mb-2 mx-2">⚠️ Página não encontrada ⚠️</h4>
        <p class="mb-6 mx-2">não encontramos a página que está tentando encontrar</p>
        <a th:href="@{/}" class="btn btn-primary mb-10">Voltar para o Início</a>
        <div class="mt-4">
            <img src="../../assets/img/illustrations/page-misc-error.png" alt="page-misc-error" width="225" class="img-fluid">
        </div>
    </div>
</div>
<div class="container-fluid misc-bg-wrapper">
    <img src="../../assets/img/illustrations/bg-shape-image-light.png" height="355" alt="page-misc-error" data-app-light-img="illustrations/bg-shape-image-light.png" data-app-dark-img="illustrations/bg-shape-image-dark.png">
</div>
<!-- /Error -->


<!-- Core JS -->
<!-- build:js assets/vendor/js/core.js -->
<script th:src="@{/assets/vendor/libs/jquery/jquery.js}"></script>
<script th:src="@{/assets/vendor/libs/popper/popper.js}"></script>
<script th:src="@{/assets/vendor/js/bootstrap.js}"></script>
<script th:src="@{/assets/vendor/libs/node-waves/node-waves.js}"></script>
<script th:src="@{/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js}"></script>
<script th:src="@{/assets/vendor/libs/hammer/hammer.js}"></script>
<script th:src="@{/assets/vendor/libs/i18n/i18n.js}"></script>
<script th:src="@{/assets/vendor/libs/typeahead-js/typeahead.js}"></script>
<script th:src="@{/assets/vendor/js/menu.js}"></script>
<!-- endbuild -->

<!-- Vendors JS -->


<!-- Language Override JS -->
<script th:src="@{/js/language-override.js}"></script>
<!-- Main JS -->
<script th:src="@{/assets/js/main.js}"></script>


<!-- Page JS -->


</body>

</html>
