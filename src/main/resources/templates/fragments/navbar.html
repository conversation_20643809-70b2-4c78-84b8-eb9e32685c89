<!DOCTYPE html>

<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>Header</title>
</head>

<body>
<!-- Navbar -->
<nav th:fragment="navbar"
     class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme"
     id="layout-navbar">

    <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0   d-xl-none ">
        <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
            <i class="ti ti-menu-2 ti-md"></i>
        </a>
    </div>

    <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">

        <!-- Search -->
        <div class="navbar-nav align-items-center">
            <div class="nav-item navbar-search-wrapper mb-0">
                <a class="nav-item nav-link search-toggler d-flex align-items-center px-0"
                   href="javascript:void(0);">
                    <i class="ti ti-search ti-md me-2 me-lg-4 ti-lg"></i>
                    <span class="d-none d-md-inline-block text-muted fw-normal">Search (Ctrl+/)</span>
                </a>
            </div>
        </div>
        <!-- /Search -->

        <ul class="navbar-nav flex-row align-items-center ms-auto">
            <!-- Language -->
            <li class="nav-item dropdown-language dropdown">
                <a class="nav-link btn btn-text-secondary btn-icon rounded-pill dropdown-toggle hide-arrow"
                   href="javascript:void(0);" data-bs-toggle="dropdown">
                    <i class='ti ti-language rounded-circle ti-md'></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item"
                           th:classappend="${#locale.language == 'pt' ? 'active' : ''}"
                           th:href="@{/changeLanguage(lang='pt')}"
                           data-text-direction="ltr">
                            <span>[[#{general.language.lang_1}]]</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item"
                           th:classappend="${#locale.language == 'en' ? 'active' : ''}"
                           th:href="@{/changeLanguage(lang='en')}"
                           data-text-direction="ltr">
                            <span>[[#{general.language.lang_2}]]</span>
                        </a>
                    </li>
                </ul>
            </li>
            <!--/ Language -->

            <!-- Style Switcher -->
            <li class="nav-item dropdown-style-switcher dropdown">
                <a class="nav-link btn btn-text-secondary btn-icon rounded-pill dropdown-toggle hide-arrow"
                   href="javascript:void(0);" data-bs-toggle="dropdown">
                    <i class='ti ti-md ti-sun'></i>
                </a>
                <ul class="dropdown-menu dropdown-menu-end dropdown-styles">
                    <li>
                        <a class="dropdown-item" href="javascript:void(0);" data-theme="light">
                            <span class="align-middle"><i class='ti ti-sun ti-md me-3'></i>Light</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="javascript:void(0);" data-theme="dark">
                                        <span class="align-middle"><i
                                                class="ti ti-moon-stars ti-md me-3"></i>Dark</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="javascript:void(0);" data-theme="system">
                                        <span class="align-middle"><i
                                                class="ti ti-device-desktop-analytics ti-md me-3"></i>System</span>
                        </a>
                    </li>
                </ul>
            </li>
            <!-- / Style Switcher-->

            <!-- Quick links  -->
            <li class="nav-item dropdown-shortcuts navbar-dropdown dropdown">
                <a class="nav-link btn btn-text-secondary btn-icon rounded-pill btn-icon dropdown-toggle hide-arrow"
                   href="javascript:void(0);" data-bs-toggle="dropdown" data-bs-auto-close="outside"
                   aria-expanded="false">
                    <i class='ti ti-layout-grid-add ti-md'></i>
                </a>
                <div class="dropdown-menu dropdown-menu-end p-0">
                    <div class="dropdown-menu-header border-bottom">
                        <div class="dropdown-header d-flex align-items-center py-3">
                            <h6 class="mb-0 me-auto">Shortcuts</h6>
                            <a href="javascript:void(0)"
                               class="btn btn-text-secondary rounded-pill btn-icon dropdown-shortcuts-add"
                               data-bs-toggle="tooltip" data-bs-placement="top" title="Add shortcuts"><i
                                    class="ti ti-plus text-heading"></i></a>
                        </div>
                    </div>
                    <div class="dropdown-shortcuts-list scrollable-container">
                        <div class="row row-bordered overflow-visible g-0">
                            <div class="dropdown-shortcuts-item col">
                    <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                      <i class="ti ti-calendar ti-26px text-heading"></i>
                    </span>
                                <a href="app-calendar.html" class="stretched-link">Calendar</a>
                                <small>Appointments</small>
                            </div>
                            <div class="dropdown-shortcuts-item col">
                    <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                      <i class="ti ti-file-dollar ti-26px text-heading"></i>
                    </span>
                                <a href="app-invoice-list.html" class="stretched-link">Invoice App</a>
                                <small>Manage Accounts</small>
                            </div>
                        </div>
                        <div class="row row-bordered overflow-visible g-0">
                            <div class="dropdown-shortcuts-item col">
                    <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                      <i class="ti ti-user ti-26px text-heading"></i>
                    </span>
                                <a href="app-user-list.html" class="stretched-link">User App</a>
                                <small>Manage Users</small>
                            </div>
                            <div class="dropdown-shortcuts-item col">
                    <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                      <i class="ti ti-users ti-26px text-heading"></i>
                    </span>
                                <a href="app-access-roles.html" class="stretched-link">Role Management</a>
                                <small>Permission</small>
                            </div>
                        </div>
                        <div class="row row-bordered overflow-visible g-0">
                            <div class="dropdown-shortcuts-item col">
                    <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                      <i class="ti ti-device-desktop-analytics ti-26px text-heading"></i>
                    </span>
                                <a href="index.html" class="stretched-link">Dashboard</a>
                                <small>User Dashboard</small>
                            </div>
                            <div class="dropdown-shortcuts-item col">
                    <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                      <i class="ti ti-settings ti-26px text-heading"></i>
                    </span>
                                <a href="pages-account-settings-account.html"
                                   class="stretched-link">Setting</a>
                                <small>Account Settings</small>
                            </div>
                        </div>
                        <div class="row row-bordered overflow-visible g-0">
                            <div class="dropdown-shortcuts-item col">
                    <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                      <i class="ti ti-help ti-26px text-heading"></i>
                    </span>
                                <a href="pages-faq.html" class="stretched-link">FAQs</a>
                                <small>FAQs & Articles</small>
                            </div>
                            <div class="dropdown-shortcuts-item col">
                    <span class="dropdown-shortcuts-icon rounded-circle mb-3">
                      <i class="ti ti-square ti-26px text-heading"></i>
                    </span>
                                <a href="modal-examples.html" class="stretched-link">Modals</a>
                                <small>Useful Popups</small>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
            <!-- Quick links -->

            <!-- Notification -->
            <li class="nav-item dropdown-notifications navbar-dropdown dropdown me-3 me-xl-2"
                hx-get="/refresh-notifications"
                hx-trigger="every 3s"
                hx-swap="none">
                <a id="notificationsDropdownToggle" class="nav-link btn btn-text-secondary btn-icon rounded-pill dropdown-toggle hide-arrow"
                   href="javascript:void(0);" data-bs-toggle="dropdown" data-bs-auto-close="outside"
                   aria-expanded="false">
              <span class="position-relative">
                <i class="ti ti-bell ti-md"></i>
                  <span th:replace="~{fragments/fragments :: notif-red-badge}"></span>
              </span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end p-0">
                    <li class="dropdown-menu-header border-bottom">
                        <div class="dropdown-header d-flex align-items-center py-3">
                            <h6 class="mb-0 me-auto pe-2">[[#{dropdown.notifications.title}]] <small class="text-muted">[[#{dropdown.notifications.latest.indicator}]]</small></h6>
                            <div class="d-flex align-items-center h6 mb-0">
                                <!--<span class="badge bg-label-primary me-2" id="totalUserNotifications" th:text="${totalUserNotifications}"></span>-->
                                <span th:replace="~{fragments/fragments :: active-items-count}"></span>
                                <img id="spinner" class="me-2 htmx-indicator" src="/img/bars.svg" alt="spinner"/>
                                <a id="btnMarkAllRead" hx-post="/mark-all-notifications-read"
                                   hx-indicator="#spinner"
                                   hx-target="#active-items-count"
                                   hx-trigger="click"
                                   hx-swap="outerHTML transition:true"
                                   class="btn btn-text-secondary rounded-pill btn-icon dropdown-notifications-all"
                                   data-bs-toggle="tooltip" data-bs-placement="top"
                                   th:title="#{modal.notifications.mark.all.read}"><i
                                        class="ti ti-mail-opened text-heading"></i></a>
                                <a id="btnMarkAllArchived" hx-post="/mark-all-notifications-archived"
                                   hx-indicator="#spinner"
                                   hx-target="#active-items-count"
                                   hx-trigger="click"
                                   hx-swap="outerHTML transition:true"
                                   class="btn btn-text-secondary rounded-pill btn-icon dropdown-notifications-all"
                                   data-bs-toggle="tooltip" data-bs-placement="top"
                                   th:title="#{modal.notifications.archive.all}"><i
                                        class="ti ti-archive text-heading"></i></a>
                            </div>
                        </div>
                    </li>
                    <li class="dropdown-notifications-list scrollable-container">
                        <ul id="userNotifications" class="list-group list-group-flush" th:remove="all-but-first">
                            <li th:insert="~{fragments/fragments :: userNotification(${item})}"
                                th:each="item : ${userNotificationsList}" th:remove="tag"></li>
                        </ul>
                    </li>
                    <li class="border-top">
                        <div class="d-grid p-4">
                            <a id="btnSeeAllNotifications"
                               class="btn btn-primary btn-sm d-flex"
                               href="javascript:void(0);"
                               data-bs-toggle="modal"
                               data-bs-target="#allNotificationsModal"
                               hx-get="/all-notifications-modal"
                               hx-ext="multi-swap"
                               hx-swap="multi:#notificationsModalContent:outerHTML,#paginationControls:outerHTML,#headerActionButtons:outerHTML,#active-items-count:outerHTML"
                               hx-trigger="click"
                               hx-indicator="#modalLoadingSpinner"
                               onclick="closeNotificationsDropdown(); event.stopPropagation();">
                                <small class="align-middle">[[#{modal.notifications.title}]]</small>
                            </a>
                        </div>
                    </li>
                </ul>
            </li>
            <!--/ Notification -->

            <!-- User -->
            <li class="nav-item navbar-dropdown dropdown-user dropdown">
                <a class="nav-link dropdown-toggle hide-arrow p-0" href="javascript:void(0);"
                   data-bs-toggle="dropdown">
                    <div class="avatar avatar-online">
                        <!--th:text="${#authentication.getPrincipal().getFullName()}"-->
                        <img alt class="rounded-circle"
                             th:src="${currentUser != null && currentUser.getPhoto() != null} ? ${'/images/' + currentUser.getPhoto()} :  '../../assets/img/avatars/silhouette.png'"/>

                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item mt-0" th:href="@{/user/edituser}">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0 me-2">
                                    <div class="avatar avatar-online">
                                        <img alt class="rounded-circle"
                                             th:src="${currentUser != null && currentUser.getPhoto() != null} ? ${'/images/' + currentUser.getPhoto()} :  '../../assets/img/avatars/silhouette.png'"/>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <th:block th:if="${#authorization.expression('isAuthenticated()')}">
                                        <h6 class="mb-0"
                                            th:text="${currentUser != null ? currentUser.getFullName() : ''}"></h6>
                                        <small class="text-muted"
                                               th:text="${mainRole}"></small>
                                    </th:block>
                                </div>
                            </div>
                        </a>
                    </li>
                    <li>
                        <div class="dropdown-divider my-1 mx-n2"></div>
                    </li>
                    <li>
                        <a class="dropdown-item" th:href="@{/user/viewuser}">
                            <i class="ti ti-user me-3 ti-md"></i><span
                                class="align-middle">[[#{page.navbar.profile}]]</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" th:href="@{/user/edituser}">
                            <i class="ti ti-settings me-3 ti-md"></i><span
                                class="align-middle">[[#{page.navbar.my.account}]]</span>
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="pages-faq.html">
                            <i class="ti ti-question-mark me-3 ti-md"></i><span
                                class="align-middle">[[#{page.navbar.faqs}]]</span>
                        </a>
                    </li>
                    <li>
                        <div class="d-grid px-2 pt-2 pb-1">
                            <a class="btn btn-sm btn-danger d-flex" th:href="@{/log_out}"
                               target="_self">
                                <small class="align-middle">[[#{page.navbar.logout}]]</small>
                                <i class="ti ti-logout ms-2 ti-14px"></i>
                            </a>
                        </div>
                    </li>
                </ul>
            </li>
            <!--/ User -->


        </ul>
    </div>


    <!-- Search Small Screens -->
    <div class="navbar-search-wrapper search-input-wrapper  d-none">
        <input type="text" class="form-control search-input container-xxl border-0" placeholder="Search..."
               aria-label="Search...">
        <i class="ti ti-x search-toggler cursor-pointer"></i>
    </div>

</nav>
<!-- / Navbar -->



<script>
    function archiveNotification(e) {
        var closestLI = e.target.parentElement.parentElement.parentElement;
        closestLI.style.display = 'none';
    }

    function refreshTotal() {
        var element = document.getElementById('redBadgeNotifications');
        element.innerHTML = element.innerHTML
    }

    function closeNotificationsDropdown() {
        // Close the notifications dropdown
        var dropdownToggle = document.getElementById('notificationsDropdownToggle');
        if (dropdownToggle) {
            var dropdown = bootstrap.Dropdown.getInstance(dropdownToggle);
            if (dropdown) {
                dropdown.hide();
            }
        }
    }

</script>

</body>

</html>
