<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}">

<!-- Notifications Modal Content Fragment -->
<div th:fragment="notificationsModalContent"
     id="notificationsModalContent"
     th:data-current-page="${notificationPage != null ? notificationPage.currentPage : 0}">
    <!-- Check if there are notifications -->
    <div th:if="${notificationPage != null and notificationPage.notifications != null and !notificationPage.notifications.isEmpty()}">
        <!-- Pagination Info -->
        <div class="pagination-info mb-3 text-muted small" th:if="${notificationPage != null}">
            [[#{modal.notifications.pagination.showing}]] <span th:text="${notificationPage.startRecord}">1</span>
            [[#{modal.notifications.pagination.to}]] <span th:text="${notificationPage.endRecord}">20</span>
            [[#{modal.notifications.pagination.of}]] <span th:text="${notificationPage.totalElements}">100</span>
            [[#{modal.notifications.pagination.notifications}]]
        </div>

        <!-- Notifications List -->
        <div class="notifications-list">
            <div th:each="notification : ${notificationPage.notifications}" class="notification-item mb-3">
                <div class="card">
                    <div class="card-body d-flex align-items-start py-3">
                        <!-- Notification Icon -->
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar">
                                <span th:if="${notification.img != null and !#strings.isEmpty(notification.img)}"
                                      class="notification-icon-span"
                                      th:utext="${notification.img}">📧</span>
                                <span th:if="${notification.img == null or #strings.isEmpty(notification.img)}"
                                      class="notification-icon-span">📧</span>
                            </div>
                        </div>

                        <!-- Notification Content -->
                        <div class="flex-grow-1">
                            <h6 class="mb-1" th:text="${notification.title}">Notification Title</h6>
                            <p class="mb-1 text-body small" th:text="${notification.subtitle}">Notification subtitle</p>
                            <small class="text-muted" th:text="${notification.createdAt}">Time</small>
                        </div>

                        <!-- Status Badge and Action Buttons -->
                        <div class="flex-shrink-0 d-flex flex-column align-items-end">
                            <!-- Status Badge -->
                            <div class="mb-2">
                                <span th:if="${!notification.isRead and !notification.archived}"
                                      class="badge bg-label-primary">[[#{modal.notifications.status.new}]]</span>
                                <span th:if="${notification.isRead and !notification.archived}"
                                      class="badge bg-label-success">[[#{modal.notifications.status.read}]]</span>
                                <span th:if="${notification.archived}"
                                      class="badge bg-label-secondary">[[#{modal.notifications.status.archived}]]</span>
                            </div>

                            <!-- Action Buttons -->
                            <div class="dropdown-notifications-actions d-flex gap-1">
                            <!-- Mark as Read Button (only show if not seen) -->
                            <a th:if="${!notification.isRead}"
                               th:hx-put="'/read-notification-modal/' + ${notification.id}"
                               hx-ext="multi-swap"
                               hx-swap="multi:#notificationsModalContent:outerHTML transition:true,#paginationControls:outerHTML transition:true,#headerActionButtons:outerHTML transition:true"
                               th:hx-vals="|{ &quot;page&quot;: ${notificationPage != null ? notificationPage.currentPage : 0} }|"
                               hx-indicator="#modalLoadingSpinner"
                               class="btn btn-text-secondary rounded-pill btn-icon btn-sm"
                               data-bs-toggle="tooltip"
                               data-bs-placement="top"
                               title="Mark as read">
                                <i class="ti ti-mail-opened text-heading"></i>
                            </a>

                                <!-- Archive Button (only show if not archived) -->
                                <a th:if="${!notification.archived}"
                                   th:hx-put="'/archive-notification-modal/' + ${notification.id}"
                                   hx-ext="multi-swap"
                                   hx-swap="multi:#notificationsModalContent:outerHTML transition:true,#paginationControls:outerHTML transition:true,#headerActionButtons:outerHTML transition:true"
                                   th:hx-vals="|{ &quot;page&quot;: ${notificationPage != null ? notificationPage.currentPage : 0} }|"
                                   hx-indicator="#modalLoadingSpinner"
                                   class="btn btn-text-secondary rounded-pill btn-icon btn-sm"
                                   data-bs-toggle="tooltip"
                                   data-bs-placement="top"
                                   title="Archive">
                                    <i class="ti ti-archive text-heading"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empty State -->
    <div th:if="${notificationPage == null or notificationPage.notifications == null or notificationPage.notifications.isEmpty()}" class="text-center py-5">
        <i class="ti ti-bell-off ti-48px text-muted mb-3"></i>
        <h6 class="text-muted">[[#{modal.notifications.empty.message}]]</h6>
    </div>
</div>

<!-- Header Action Buttons Fragment -->
<div th:fragment="headerActionButtons" id="headerActionButtons" hx-swap-oob="true">
    <div class="d-flex align-items-center gap-2">
        <!-- Active Items Count (modal-specific to avoid conflicts with navbar) -->
        <span id="modal-active-items-count">
            <th:block th:unless="${numberOfActiveItems == 1}">
                <span class="badge bg-label-primary me-2"><strong th:text="${numberOfActiveItems}">0</strong> [[#{dropdown.notifications.new.plural}]]</span>
            </th:block>
            <th:block th:if="${numberOfActiveItems == 1}">
                <span class="badge bg-label-primary me-2"><strong>1</strong> [[#{dropdown.notifications.new.singular}]]</span>
            </th:block>
        </span>

        <!-- Mark All as Read Button -->
        <a id="btnMarkAllReadModal"
           hx-post="/mark-all-notifications-read-modal"
           hx-ext="multi-swap"
           hx-swap="multi:#notificationsModalContent:outerHTML transition:true,#paginationControls:outerHTML transition:true,#headerActionButtons:outerHTML transition:true"
           hx-trigger="click"
           hx-disable-elt="#notificationsModalContent"
           hx-indicator="#modalLoadingSpinner"
           class="btn btn-text-secondary rounded-pill btn-icon btn-sm"
           data-bs-toggle="tooltip"
           data-bs-placement="top"
           th:title="#{modal.notifications.mark.all.read}">
            <i class="ti ti-mail-opened text-heading"></i>
        </a>

        <!-- Archive All Button -->
        <a id="btnMarkAllArchivedModal"
           hx-post="/mark-all-notifications-archived-modal"
           hx-ext="multi-swap"
           hx-swap="multi:#notificationsModalContent:outerHTML transition:true,#paginationControls:outerHTML transition:true,#headerActionButtons:outerHTML transition:true"
           hx-trigger="click"
           hx-disable-elt="#notificationsModalContent"
           hx-indicator="#modalLoadingSpinner"
           class="btn btn-text-secondary rounded-pill btn-icon btn-sm"
           data-bs-toggle="tooltip"
           data-bs-placement="top"
           th:title="#{modal.notifications.archive.all}">
            <i class="ti ti-archive text-heading"></i>
        </a>
    </div>
</div>

<!-- Pagination Controls Fragment for Modal Footer -->
<div th:fragment="paginationControls" id="paginationControls" hx-swap-oob="true">
    <div th:if="${notificationPage != null and notificationPage.totalPages > 1}" class="pagination-controls d-flex justify-content-between align-items-center w-100">
        <!-- Left Navigation Group -->
        <div class="d-flex align-items-center gap-2">
            <!-- First Page Button -->
            <button th:if="${notificationPage != null and notificationPage.currentPage > 0}"
                    class="btn btn-outline-secondary btn-sm"
                    hx-get="/all-notifications-modal"
                    hx-vals='{ "page": 0 }'
                    hx-ext="multi-swap"
                    hx-indicator="#modalLoadingSpinner"
                    hx-swap="multi:#notificationsModalContent:outerHTML transition:true,#paginationControls:outerHTML transition:true,#headerActionButtons:outerHTML transition:true"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="[[#{modal.notifications.pagination.first}]]">
                <i class="ti ti-chevrons-left"></i>
            </button>
            <button th:unless="${notificationPage != null and notificationPage.currentPage > 0}"
                    class="btn btn-outline-secondary btn-sm" disabled
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="[[#{modal.notifications.pagination.first}]]">
                <i class="ti ti-chevrons-left"></i>
            </button>

            <!-- Previous Button -->
            <button th:if="${notificationPage != null and notificationPage.hasPrevious}"
                    class="btn btn-outline-secondary btn-sm"
                    hx-get="/all-notifications-modal"
                    th:hx-vals="|{ &quot;page&quot;: ${notificationPage.currentPage - 1} }|"
                    hx-ext="multi-swap"
                    hx-indicator="#modalLoadingSpinner"
                    hx-swap="multi:#notificationsModalContent:outerHTML transition:true,#paginationControls:outerHTML transition:true,#headerActionButtons:outerHTML transition:true">
                <i class="ti ti-chevron-left"></i> [[#{modal.notifications.pagination.previous}]]
            </button>
            <button th:unless="${notificationPage != null and notificationPage.hasPrevious}"
                    class="btn btn-outline-secondary btn-sm" disabled>
                <i class="ti ti-chevron-left"></i> [[#{modal.notifications.pagination.previous}]]
            </button>
        </div>

        <!-- Page Info -->
        <span class="text-muted small" th:if="${notificationPage != null}">
            [[#{modal.notifications.pagination.page}]] <span th:text="${notificationPage.currentPage + 1}">1</span>
            [[#{modal.notifications.pagination.of}]] <span th:text="${notificationPage.totalPages}">5</span>
        </span>

        <!-- Right Navigation Group -->
        <div class="d-flex align-items-center gap-2">
            <!-- Next Button -->
            <button th:if="${notificationPage != null and notificationPage.hasNext}"
                    class="btn btn-outline-secondary btn-sm"
                    hx-get="/all-notifications-modal"
                    th:hx-vals="|{ &quot;page&quot;: ${notificationPage.currentPage + 1} }|"
                    hx-ext="multi-swap"
                    hx-indicator="#modalLoadingSpinner"
                    hx-swap="multi:#notificationsModalContent:outerHTML transition:true,#paginationControls:outerHTML transition:true,#headerActionButtons:outerHTML transition:true">
                [[#{modal.notifications.pagination.next}]] <i class="ti ti-chevron-right"></i>
            </button>
            <button th:unless="${notificationPage != null and notificationPage.hasNext}"
                    class="btn btn-outline-secondary btn-sm" disabled>
                [[#{modal.notifications.pagination.next}]] <i class="ti ti-chevron-right"></i>
            </button>

            <!-- Last Page Button -->
            <button th:if="${notificationPage != null and notificationPage.currentPage < (notificationPage.totalPages - 1)}"
                    class="btn btn-outline-secondary btn-sm"
                    hx-get="/all-notifications-modal"
                    th:hx-vals="|{ &quot;page&quot;: ${notificationPage.totalPages - 1} }|"
                    hx-ext="multi-swap"
                    hx-indicator="#modalLoadingSpinner"
                    hx-swap="multi:#notificationsModalContent:outerHTML transition:true,#paginationControls:outerHTML transition:true,#headerActionButtons:outerHTML transition:true"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="[[#{modal.notifications.pagination.last}]]">
                <i class="ti ti-chevrons-right"></i>
            </button>
            <button th:unless="${notificationPage != null and notificationPage.currentPage < (notificationPage.totalPages - 1)}"
                    class="btn btn-outline-secondary btn-sm" disabled
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="[[#{modal.notifications.pagination.last}]]">
                <i class="ti ti-chevrons-right"></i>
            </button>
        </div>
    </div>
</div>

<style>
/* Custom styles for notifications modal */
.notification-item {
    min-height: 80px; /* Fixed height per notification */
}

.notification-item .card {
    border: 1px solid rgba(67, 89, 113, 0.1);
    transition: all 0.2s ease;
}

.notification-item .card:hover {
    border-color: rgba(67, 89, 113, 0.2);
    box-shadow: 0 2px 4px rgba(67, 89, 113, 0.1);
}

.notifications-list {
    /* This ensures consistent spacing and allows for scrolling */
    max-height: none; /* Let the parent modal-body handle the scrolling */
}

/* Ensure proper spacing for action buttons */
.dropdown-notifications-actions .btn-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Notification icon styling */
.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(67, 89, 113, 0.1);
    border: 1px solid rgba(67, 89, 113, 0.2);
}

.notification-icon-span {
    font-size: 18px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Action buttons container styling */
.dropdown-notifications-actions {
    gap: 4px;
}

/* Modal header styling */
.modal-notifications-header {
    background-color: rgba(67, 89, 113, 0.02);
    border-radius: 8px;
    padding: 16px;
    margin: -8px -8px 16px -8px;
}

.modal-notifications-header h6 {
    color: #435971;
    font-weight: 600;
}

.modal-notifications-header .btn-icon {
    width: 32px;
    height: 32px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Spinner container and styling */
.spinner-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    min-height: 24px;
}

/* Let HTMX handle spinner visibility automatically */
.modal-notifications-header .htmx-indicator {
    margin-left: 8px;
    margin-right: 8px;
}

/* Pagination styling for modal footer */
.pagination-info {
    font-size: 0.875rem;
    color: #6c757d;
    text-align: center;
}

.pagination-controls {
    padding: 8px 0;
}

.pagination-controls .btn {
    min-width: 100px;
}

.pagination-controls .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* First/Last page buttons with icon only */
.pagination-controls .btn:has(.ti-chevrons-left),
.pagination-controls .btn:has(.ti-chevrons-right) {
    min-width: 40px;
    padding: 0.375rem 0.5rem;
}

/* Navigation button groups */
.pagination-controls .d-flex.gap-2 {
    flex-shrink: 0;
}

/* Modal footer specific styling */
.modal-footer {
    flex-direction: column;
}

#paginationControlsContainer {
    border-top: 1px solid rgba(67, 89, 113, 0.1);
    padding-top: 16px;
}
</style>

</html>
