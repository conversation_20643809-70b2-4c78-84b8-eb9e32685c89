<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org"
      th:lang="${#locale.language}">


<span th:fragment="notif-red-badge"
      id="notif-red-badge"
      hx-swap-oob="true">
    <!--<div id="redBadgeNotifications" th:if="${not #strings.equals(totalUserNotifications,'0 Novas')}">
        <span class="badge rounded-pill bg-danger badge-dot badge-notifications border"></span>
    </div>-->
    <th:block th:if="${numberOfActiveItems >= 1}">
        <span class="badge rounded-pill bg-danger badge-dot badge-notifications border"></span>
    </th:block>
</span>


<li th:fragment="userNotification(item)" class="list-group-item list-group-item-action dropdown-notifications-item"
    th:id="'userNotification-' + ${item.id}"
    th:classappend="${item.isRead} ? 'marked-as-read' : ''">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <div class="avatar" th:utext="${item.img}"></div>
        </div>
        <div class="flex-grow-1">
            <h6 class="mb-1 small" th:text="${item.title}"></h6>
            <small class="mb-1 d-block text-body" th:text="${item.subtitle}"></small>
            <small class="text-muted" th:text="${item.createdAt}"></small>
        </div>
        <div class="flex-shrink-0 dropdown-notifications-actions">
            <!-- Mark as Read Button (only show if not seen) -->
            <a th:if="${!item.isRead}"
               id="btnRead"
               th:hx-put="'/read-notification/' + ${item.id} "
               hx-ext="multi-swap"
               th:hx-swap="'multi:#userNotification-' + ${item.id} + ':outerHTML,#active-items-count:outerHTML,#notif-red-badge:outerHTML'"
               hx-trigger="click"
               class="dropdown-notifications-read"
               data-bs-toggle="tooltip"
               data-bs-placement="top"
               title="Mark as read">
                <span class="badge badge-dot"></span>
            </a>

            <!-- Archive Button (only show if not archived) -->
            <a th:if="${!item.archived}"
               id="btnArchive"
               th:hx-put="'/archive-notification/' + ${item.id} "
               hx-ext="multi-swap"
               hx-swap="multi:#userNotifications:outerHTML,#active-items-count:outerHTML,#notif-red-badge:outerHTML"
               hx-trigger="click"
               class="dropdown-notifications-archive"
               data-bs-toggle="tooltip"
               data-bs-placement="top"
               title="Archive">
                <span class="ti ti-x"></span>
            </a>
        </div>
    </div>
</li>


<span th:fragment="active-items-count"
      id="active-items-count"
      hx-swap-oob="true">
        <th:block th:unless="${numberOfActiveItems == 1}">
            <span class="badge bg-label-primary me-2"><strong th:text="${numberOfActiveItems}">0</strong> [[#{dropdown.notifications.new.plural}]]</span>
        </th:block>
        <th:block th:if="${numberOfActiveItems == 1}">
            <span class="badge bg-label-primary me-2"><strong>1</strong> [[#{dropdown.notifications.new.singular}]]</span>
        </th:block>
</span>

<!-- Navbar Notifications List Fragment -->
<ul th:fragment="userNotificationsList" id="userNotifications" class="list-group list-group-flush" hx-swap-oob="true">
    <li th:insert="~{fragments/fragments :: userNotification(${item})}"
        th:each="item : ${userNotificationsList}" th:remove="tag"></li>
</ul>

</html>
