<!DOCTYPE html>

<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>Common Scripts</title>
</head>

<body>

<!-- Common Core JS -->
<th:block th:fragment="core-js">
    <!-- build:js assets/vendor/js/core.js -->
    <script th:src="@{/assets/vendor/libs/jquery/jquery.js}"></script>
    <script th:src="@{/assets/vendor/libs/popper/popper.js}"></script>
    <script th:src="@{/assets/vendor/js/bootstrap.js}"></script>
    <script th:src="@{/assets/vendor/libs/node-waves/node-waves.js}"></script>
    <script th:src="@{/assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js}"></script>
    <script th:src="@{/assets/vendor/libs/hammer/hammer.js}"></script>
    <script th:src="@{/assets/vendor/libs/i18n/i18n.js}"></script>
    <script th:src="@{/assets/vendor/libs/typeahead-js/typeahead.js}"></script>
    <script th:src="@{/assets/vendor/js/menu.js}"></script>
    <!-- endbuild -->
</th:block>

<!-- Common Head JS -->
<th:block th:fragment="head-js">
    <!-- Helpers -->
    <script th:src="@{/assets/vendor/js/helpers.js}"></script>
    <!--! Template customizer & Theme config files MUST be included after core stylesheets and helpers.js in the <head> section -->

    <!--? Template customizer: To hide customizer set displayCustomizer value false in config.js.  -->
    <script th:src="@{/assets/vendor/js/template-customizer.js}"></script>
</th:block>

<!-- Main JS -->
<th:block th:fragment="main-js">
    <!-- Language Override JS -->
    <script th:src="@{/js/language-override.js}"></script>
    <!-- Main JS -->
    <script th:src="@{/assets/js/main.js}"></script>
</th:block>

</body>

</html>
