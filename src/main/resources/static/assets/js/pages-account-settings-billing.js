"use strict";document.addEventListener("DOMContentLoaded",function(e){var t,n,i,o;t=document.querySelector(".credit-card-mask"),n=document.querySelector(".expiry-date-mask"),i=document.querySelector(".cvv-code-mask"),t&&new Cleave(t,{creditCard:!0,onCreditCardTypeChanged:function(e){document.querySelector(".card-type").innerHTML=""!=e&&"unknown"!=e?'<img src="'+assetsPath+"img/icons/payments/"+e+'-cc.png" height="28"/>':""}}),n&&new Cleave(n,{date:!0,delimiter:"/",datePattern:["m","y"]}),i&&new Cleave(i,{numeral:!0,numeralPositiveOnly:!0}),t=document.getElementById("formAccountSettings"),n=document.querySelector(".mobile-number"),i=document.querySelector(".zip-code"),o=document.getElementById("creditCardForm"),t&&FormValidation.formValidation(t,{fields:{companyName:{validators:{notEmpty:{message:"Please enter company name"}}},billingEmail:{validators:{notEmpty:{message:"Please enter billing email"},emailAddress:{message:"Please enter valid email address"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-6"}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus}}),o&&FormValidation.formValidation(o,{fields:{paymentCard:{validators:{notEmpty:{message:"Please enter your credit card number"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:""}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus},init:e=>{e.on("plugins.message.placed",function(e){e.element.parentElement.classList.contains("input-group")&&e.element.parentElement.insertAdjacentElement("afterend",e.messageElement)})}}),(t=document.querySelector(".cancel-subscription"))&&(t.onclick=function(){Swal.fire({text:"Are you sure you would like to cancel your subscription?",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes",customClass:{confirmButton:"btn btn-primary me-2 waves-effect waves-light",cancelButton:"btn btn-label-secondary waves-effect waves-light"},buttonsStyling:!1}).then(function(e){e.value?Swal.fire({icon:"success",title:"Unsubscribed!",text:"Your subscription cancelled successfully.",customClass:{confirmButton:"btn btn-success waves-effect waves-light"}}):e.dismiss===Swal.DismissReason.cancel&&Swal.fire({title:"Cancelled",text:"Unsubscription Cancelled!!",icon:"error",customClass:{confirmButton:"btn btn-success waves-effect waves-light"}})})}),n&&new Cleave(n,{phone:!0,phoneRegionCode:"US"}),i&&new Cleave(i,{delimiter:"",numeral:!0})}),$(function(){var e=$(".select2");e.length&&e.each(function(){var e=$(this);e.wrap('<div class="position-relative"></div>'),e.select2({dropdownParent:e.parent()})})});