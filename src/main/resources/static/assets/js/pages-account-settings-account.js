"use strict";
document.addEventListener("DOMContentLoaded", function (e) {
    {
        var t = document.querySelector("#phoneNumber");
        t &&
        new Cleave(
            t,
            {
                phoneRegionCode: "BR",
                delimiters: ['(', ') ', '-'],
                blocks: [0, 2, 5, 4],
                numericOnly: true,
                delimiterLazyShow: true
            }
        );

        let e = document.getElementById("uploadedAvatar");

        const l = document.querySelector(".account-file-input"), c = document.querySelector(".account-image-reset");
        if (e) {
            const r = e.src;
            l.onchange = () => {
                l.files[0] && (e.src = window.URL.createObjectURL(l.files[0]))
            }, c.onclick = () => {
                l.value = "", e.src = r
            }
        }
    }
})
    , $(function () {
    var e = $(".select2");
    e.length && e.each(function () {
        var e = $(this);
        e.wrap('<div class="position-relative"></div>'), e.select2({
            dropdownParent: e.parent(),
            minimumResultsForSearch: 0,
            matcher: function(params, data) {
                // If there are no search terms, return all data
                if (!params.term || $.trim(params.term) === '') {
                    return data;
                }

                // Do not display the item if there is no 'text' property
                if (typeof data.text === 'undefined') {
                    return null;
                }

                // Case-insensitive search
                if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                    return data;
                }

                // Return `null` if the term should not be displayed
                return null;
            }
        });

        // Fix accessibility issue with aria-hidden
        // Remove aria-hidden from the container and use inert attribute instead
        setTimeout(function() {
            const select2Container = e.next('.select2-container');
            if (select2Container.length) {
                select2Container.removeAttr('aria-hidden');

                // Find any elements with aria-hidden that might contain focusable elements
                select2Container.find('[aria-hidden="true"]').each(function() {
                    const $this = $(this);
                    // If this element contains focusable elements and is not meant to be interactive
                    if ($this.find('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])').length) {
                        $this.removeAttr('aria-hidden');
                        // Use inert attribute if supported
                        if ('inert' in document.createElement('div')) {
                            $this.attr('inert', '');
                        }
                    }
                });
            }
        }, 100)
    })
});
