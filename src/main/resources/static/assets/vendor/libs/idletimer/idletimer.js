/*! For license information please see idletimer.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var i=t();for(var n in i)("object"==typeof exports?exports:e)[n]=i[n]}}(self,(function(){return function(){var e;function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}return(e=jQuery).idleTimer=function(i,n){var r;"object"===t(i)?(r=i,i=null):"number"==typeof i&&(r={timeout:i},i=null),n=n||document,r=e.extend({idle:!1,timeout:3e4,events:"mousemove keydown wheel DOMMouseScroll mousewheel mousedown touchstart touchmove MSPointerDown MSPointerMove"},r);var o=e(n),l=o.data("idleTimerObj")||{},a=function(t){var i=e.data(n,"idleTimerObj")||{};i.idle=!i.idle,i.olddate=+new Date;var r=e.Event((i.idle?"idle":"active")+".idleTimer");e(n).trigger(r,[n,e.extend({},i),t])},d=function(t){var i=e.data(n,"idleTimerObj")||{};if(("storage"!==t.type||t.originalEvent.key===i.timerSyncId)&&null==i.remaining){if("mousemove"===t.type){if(t.pageX===i.pageX&&t.pageY===i.pageY)return;if(void 0===t.pageX&&void 0===t.pageY)return;if(+new Date-i.olddate<200)return}clearTimeout(i.tId),i.idle&&a(t),i.lastActive=+new Date,i.pageX=t.pageX,i.pageY=t.pageY,"storage"!==t.type&&i.timerSyncId&&"undefined"!=typeof localStorage&&localStorage.setItem(i.timerSyncId,i.lastActive),i.tId=setTimeout(a,i.timeout)}},u=function(){var t=e.data(n,"idleTimerObj")||{};t.idle=t.idleBackup,t.olddate=+new Date,t.lastActive=t.olddate,t.remaining=null,clearTimeout(t.tId),t.idle||(t.tId=setTimeout(a,t.timeout))};if(null===i&&void 0!==l.idle)return u(),o;if(null===i);else{if(null!==i&&void 0===l.idle)return!1;if("destroy"===i)return function(){var t=e.data(n,"idleTimerObj")||{};clearTimeout(t.tId),o.removeData("idleTimerObj"),o.off("._idleTimer")}(),o;if("pause"===i)return function(){var t=e.data(n,"idleTimerObj")||{};null==t.remaining&&(t.remaining=t.timeout-(+new Date-t.olddate),clearTimeout(t.tId))}(),o;if("resume"===i)return function(){var t=e.data(n,"idleTimerObj")||{};null!=t.remaining&&(t.idle||(t.tId=setTimeout(a,t.remaining)),t.remaining=null)}(),o;if("reset"===i)return u(),o;if("getRemainingTime"===i)return function(){var t=e.data(n,"idleTimerObj")||{};if(t.idle)return 0;if(null!=t.remaining)return t.remaining;var i=t.timeout-(+new Date-t.lastActive);return i<0&&(i=0),i}();if("getElapsedTime"===i)return+new Date-l.olddate;if("getLastActiveTime"===i)return l.lastActive;if("isIdle"===i)return l.idle}return o.on((r.events+" ").split(" ").join("._idleTimer ").trim(),(function(e){d(e)})),r.timerSyncId&&e(window).on("storage",d),(l=e.extend({},{olddate:+new Date,lastActive:+new Date,idle:r.idle,idleBackup:r.idle,timeout:r.timeout,remaining:null,timerSyncId:r.timerSyncId,tId:null,pageX:null,pageY:null})).idle||(l.tId=setTimeout(a,l.timeout)),e.data(n,"idleTimerObj",l),o},e.fn.idleTimer=function(t){return this[0]?e.idleTimer(t,this[0]):this},{}}()}));