/*! For license information please see bootstrap.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n=t();for(var i in n)("object"==typeof exports?exports:e)[i]=n[i]}}(self,(function(){return function(){"use strict";var e={d:function(t,n){for(var i in n)e.o(n,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:n[i]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{bootstrap:function(){return i}});var n={};e.r(n),e.d(n,{afterMain:function(){return E},afterRead:function(){return b},afterWrite:function(){return T},applyStyles:function(){return D},arrow:function(){return Z},auto:function(){return c},basePlacements:function(){return l},beforeMain:function(){return w},beforeRead:function(){return _},beforeWrite:function(){return A},bottom:function(){return o},clippingParents:function(){return d},computeStyles:function(){return ie},createPopper:function(){return Pe},createPopperBase:function(){return je},createPopperLite:function(){return De},detectOverflow:function(){return _e},end:function(){return f},eventListeners:function(){return oe},flip:function(){return ye},hide:function(){return ke},left:function(){return s},main:function(){return k},modifierPhases:function(){return C},offset:function(){return Ee},placements:function(){return g},popper:function(){return p},popperGenerator:function(){return Le},popperOffsets:function(){return Ae},preventOverflow:function(){return Oe},read:function(){return y},reference:function(){return v},right:function(){return a},start:function(){return u},top:function(){return r},variationPlacements:function(){return m},viewport:function(){return h},write:function(){return O}});var i={};e.r(i),e.d(i,{Alert:function(){return $t},Button:function(){return en},Carousel:function(){return Dn},Collapse:function(){return Un},Dropdown:function(){return _i},Modal:function(){return Zi},Offcanvas:function(){return gr},Popover:function(){return Br},ScrollSpy:function(){return $r},Tab:function(){return wo},Toast:function(){return Mo},Tooltip:function(){return Mr}});var r="top",o="bottom",a="right",s="left",c="auto",l=[r,o,a,s],u="start",f="end",d="clippingParents",h="viewport",p="popper",v="reference",m=l.reduce((function(e,t){return e.concat([t+"-"+u,t+"-"+f])}),[]),g=[].concat(l,[c]).reduce((function(e,t){return e.concat([t,t+"-"+u,t+"-"+f])}),[]),_="beforeRead",y="read",b="afterRead",w="beforeMain",k="main",E="afterMain",A="beforeWrite",O="write",T="afterWrite",C=[_,y,b,w,k,E,A,O,T];function x(e){return e?(e.nodeName||"").toLowerCase():null}function S(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function L(e){return e instanceof S(e).Element||e instanceof Element}function j(e){return e instanceof S(e).HTMLElement||e instanceof HTMLElement}function P(e){return"undefined"!=typeof ShadowRoot&&(e instanceof S(e).ShadowRoot||e instanceof ShadowRoot)}var D={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},i=t.attributes[e]||{},r=t.elements[e];j(r)&&x(r)&&(Object.assign(r.style,n),Object.keys(i).forEach((function(e){var t=i[e];!1===t?r.removeAttribute(e):r.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var i=t.elements[e],r=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});j(i)&&x(i)&&(Object.assign(i.style,o),Object.keys(r).forEach((function(e){i.removeAttribute(e)})))}))}},requires:["computeStyles"]};function I(e){return e.split("-")[0]}var N=Math.max,M=Math.min,F=Math.round;function H(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function B(){return!/^((?!chrome|android).)*safari/i.test(H())}function W(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var i=e.getBoundingClientRect(),r=1,o=1;t&&j(e)&&(r=e.offsetWidth>0&&F(i.width)/e.offsetWidth||1,o=e.offsetHeight>0&&F(i.height)/e.offsetHeight||1);var a=(L(e)?S(e):window).visualViewport,s=!B()&&n,c=(i.left+(s&&a?a.offsetLeft:0))/r,l=(i.top+(s&&a?a.offsetTop:0))/o,u=i.width/r,f=i.height/o;return{width:u,height:f,top:l,right:c+u,bottom:l+f,left:c,x:c,y:l}}function R(e){var t=W(e),n=e.offsetWidth,i=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-i)<=1&&(i=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:i}}function z(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&P(n)){var i=t;do{if(i&&e.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function q(e){return S(e).getComputedStyle(e)}function V(e){return["table","td","th"].indexOf(x(e))>=0}function K(e){return((L(e)?e.ownerDocument:e.document)||window.document).documentElement}function Q(e){return"html"===x(e)?e:e.assignedSlot||e.parentNode||(P(e)?e.host:null)||K(e)}function U(e){return j(e)&&"fixed"!==q(e).position?e.offsetParent:null}function X(e){for(var t=S(e),n=U(e);n&&V(n)&&"static"===q(n).position;)n=U(n);return n&&("html"===x(n)||"body"===x(n)&&"static"===q(n).position)?t:n||function(e){var t=/firefox/i.test(H());if(/Trident/i.test(H())&&j(e)&&"fixed"===q(e).position)return null;var n=Q(e);for(P(n)&&(n=n.host);j(n)&&["html","body"].indexOf(x(n))<0;){var i=q(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||t&&"filter"===i.willChange||t&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(e)||t}function Y(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function $(e,t,n){return N(e,M(t,n))}function G(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function J(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}var Z={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,i=e.name,c=e.options,u=n.elements.arrow,f=n.modifiersData.popperOffsets,d=I(n.placement),h=Y(d),p=[s,a].indexOf(d)>=0?"height":"width";if(u&&f){var v=function(e,t){return G("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:J(e,l))}(c.padding,n),m=R(u),g="y"===h?r:s,_="y"===h?o:a,y=n.rects.reference[p]+n.rects.reference[h]-f[h]-n.rects.popper[p],b=f[h]-n.rects.reference[h],w=X(u),k=w?"y"===h?w.clientHeight||0:w.clientWidth||0:0,E=y/2-b/2,A=v[g],O=k-m[p]-v[_],T=k/2-m[p]/2+E,C=$(A,T,O),x=h;n.modifiersData[i]=((t={})[x]=C,t.centerOffset=C-T,t)}},effect:function(e){var t=e.state,n=e.options.element,i=void 0===n?"[data-popper-arrow]":n;null!=i&&("string"!=typeof i||(i=t.elements.popper.querySelector(i)))&&z(t.elements.popper,i)&&(t.elements.arrow=i)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function ee(e){return e.split("-")[1]}var te={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ne(e){var t,n=e.popper,i=e.popperRect,c=e.placement,l=e.variation,u=e.offsets,d=e.position,h=e.gpuAcceleration,p=e.adaptive,v=e.roundOffsets,m=e.isFixed,g=u.x,_=void 0===g?0:g,y=u.y,b=void 0===y?0:y,w="function"==typeof v?v({x:_,y:b}):{x:_,y:b};_=w.x,b=w.y;var k=u.hasOwnProperty("x"),E=u.hasOwnProperty("y"),A=s,O=r,T=window;if(p){var C=X(n),x="clientHeight",L="clientWidth";C===S(n)&&"static"!==q(C=K(n)).position&&"absolute"===d&&(x="scrollHeight",L="scrollWidth"),(c===r||(c===s||c===a)&&l===f)&&(O=o,b-=(m&&C===T&&T.visualViewport?T.visualViewport.height:C[x])-i.height,b*=h?1:-1),c!==s&&(c!==r&&c!==o||l!==f)||(A=a,_-=(m&&C===T&&T.visualViewport?T.visualViewport.width:C[L])-i.width,_*=h?1:-1)}var j,P=Object.assign({position:d},p&&te),D=!0===v?function(e,t){var n=e.x,i=e.y,r=t.devicePixelRatio||1;return{x:F(n*r)/r||0,y:F(i*r)/r||0}}({x:_,y:b},S(n)):{x:_,y:b};return _=D.x,b=D.y,h?Object.assign({},P,((j={})[O]=E?"0":"",j[A]=k?"0":"",j.transform=(T.devicePixelRatio||1)<=1?"translate("+_+"px, "+b+"px)":"translate3d("+_+"px, "+b+"px, 0)",j)):Object.assign({},P,((t={})[O]=E?b+"px":"",t[A]=k?_+"px":"",t.transform="",t))}var ie={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,i=n.gpuAcceleration,r=void 0===i||i,o=n.adaptive,a=void 0===o||o,s=n.roundOffsets,c=void 0===s||s,l={placement:I(t.placement),variation:ee(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ne(Object.assign({},l,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:c})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ne(Object.assign({},l,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},re={passive:!0},oe={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,i=e.options,r=i.scroll,o=void 0===r||r,a=i.resize,s=void 0===a||a,c=S(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&l.forEach((function(e){e.addEventListener("scroll",n.update,re)})),s&&c.addEventListener("resize",n.update,re),function(){o&&l.forEach((function(e){e.removeEventListener("scroll",n.update,re)})),s&&c.removeEventListener("resize",n.update,re)}},data:{}},ae={left:"right",right:"left",bottom:"top",top:"bottom"};function se(e){return e.replace(/left|right|bottom|top/g,(function(e){return ae[e]}))}var ce={start:"end",end:"start"};function le(e){return e.replace(/start|end/g,(function(e){return ce[e]}))}function ue(e){var t=S(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function fe(e){return W(K(e)).left+ue(e).scrollLeft}function de(e){var t=q(e),n=t.overflow,i=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+i)}function he(e){return["html","body","#document"].indexOf(x(e))>=0?e.ownerDocument.body:j(e)&&de(e)?e:he(Q(e))}function pe(e,t){var n;void 0===t&&(t=[]);var i=he(e),r=i===(null==(n=e.ownerDocument)?void 0:n.body),o=S(i),a=r?[o].concat(o.visualViewport||[],de(i)?i:[]):i,s=t.concat(a);return r?s:s.concat(pe(Q(a)))}function ve(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function me(e,t,n){return t===h?ve(function(e,t){var n=S(e),i=K(e),r=n.visualViewport,o=i.clientWidth,a=i.clientHeight,s=0,c=0;if(r){o=r.width,a=r.height;var l=B();(l||!l&&"fixed"===t)&&(s=r.offsetLeft,c=r.offsetTop)}return{width:o,height:a,x:s+fe(e),y:c}}(e,n)):L(t)?function(e,t){var n=W(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):ve(function(e){var t,n=K(e),i=ue(e),r=null==(t=e.ownerDocument)?void 0:t.body,o=N(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),a=N(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),s=-i.scrollLeft+fe(e),c=-i.scrollTop;return"rtl"===q(r||n).direction&&(s+=N(n.clientWidth,r?r.clientWidth:0)-o),{width:o,height:a,x:s,y:c}}(K(e)))}function ge(e){var t,n=e.reference,i=e.element,c=e.placement,l=c?I(c):null,d=c?ee(c):null,h=n.x+n.width/2-i.width/2,p=n.y+n.height/2-i.height/2;switch(l){case r:t={x:h,y:n.y-i.height};break;case o:t={x:h,y:n.y+n.height};break;case a:t={x:n.x+n.width,y:p};break;case s:t={x:n.x-i.width,y:p};break;default:t={x:n.x,y:n.y}}var v=l?Y(l):null;if(null!=v){var m="y"===v?"height":"width";switch(d){case u:t[v]=t[v]-(n[m]/2-i[m]/2);break;case f:t[v]=t[v]+(n[m]/2-i[m]/2)}}return t}function _e(e,t){void 0===t&&(t={});var n=t,i=n.placement,s=void 0===i?e.placement:i,c=n.strategy,u=void 0===c?e.strategy:c,f=n.boundary,m=void 0===f?d:f,g=n.rootBoundary,_=void 0===g?h:g,y=n.elementContext,b=void 0===y?p:y,w=n.altBoundary,k=void 0!==w&&w,E=n.padding,A=void 0===E?0:E,O=G("number"!=typeof A?A:J(A,l)),T=b===p?v:p,C=e.rects.popper,S=e.elements[k?T:b],P=function(e,t,n,i){var r="clippingParents"===t?function(e){var t=pe(Q(e)),n=["absolute","fixed"].indexOf(q(e).position)>=0&&j(e)?X(e):e;return L(n)?t.filter((function(e){return L(e)&&z(e,n)&&"body"!==x(e)})):[]}(e):[].concat(t),o=[].concat(r,[n]),a=o[0],s=o.reduce((function(t,n){var r=me(e,n,i);return t.top=N(r.top,t.top),t.right=M(r.right,t.right),t.bottom=M(r.bottom,t.bottom),t.left=N(r.left,t.left),t}),me(e,a,i));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}(L(S)?S:S.contextElement||K(e.elements.popper),m,_,u),D=W(e.elements.reference),I=ge({reference:D,element:C,strategy:"absolute",placement:s}),F=ve(Object.assign({},C,I)),H=b===p?F:D,B={top:P.top-H.top+O.top,bottom:H.bottom-P.bottom+O.bottom,left:P.left-H.left+O.left,right:H.right-P.right+O.right},R=e.modifiersData.offset;if(b===p&&R){var V=R[s];Object.keys(B).forEach((function(e){var t=[a,o].indexOf(e)>=0?1:-1,n=[r,o].indexOf(e)>=0?"y":"x";B[e]+=V[n]*t}))}return B}var ye={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,i=e.name;if(!t.modifiersData[i]._skip){for(var f=n.mainAxis,d=void 0===f||f,h=n.altAxis,p=void 0===h||h,v=n.fallbackPlacements,_=n.padding,y=n.boundary,b=n.rootBoundary,w=n.altBoundary,k=n.flipVariations,E=void 0===k||k,A=n.allowedAutoPlacements,O=t.options.placement,T=I(O),C=v||(T!==O&&E?function(e){if(I(e)===c)return[];var t=se(e);return[le(e),t,le(t)]}(O):[se(O)]),x=[O].concat(C).reduce((function(e,n){return e.concat(I(n)===c?function(e,t){void 0===t&&(t={});var n=t,i=n.placement,r=n.boundary,o=n.rootBoundary,a=n.padding,s=n.flipVariations,c=n.allowedAutoPlacements,u=void 0===c?g:c,f=ee(i),d=f?s?m:m.filter((function(e){return ee(e)===f})):l,h=d.filter((function(e){return u.indexOf(e)>=0}));0===h.length&&(h=d);var p=h.reduce((function(t,n){return t[n]=_e(e,{placement:n,boundary:r,rootBoundary:o,padding:a})[I(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:n,boundary:y,rootBoundary:b,padding:_,flipVariations:E,allowedAutoPlacements:A}):n)}),[]),S=t.rects.reference,L=t.rects.popper,j=new Map,P=!0,D=x[0],N=0;N<x.length;N++){var M=x[N],F=I(M),H=ee(M)===u,B=[r,o].indexOf(F)>=0,W=B?"width":"height",R=_e(t,{placement:M,boundary:y,rootBoundary:b,altBoundary:w,padding:_}),z=B?H?a:s:H?o:r;S[W]>L[W]&&(z=se(z));var q=se(z),V=[];if(d&&V.push(R[F]<=0),p&&V.push(R[z]<=0,R[q]<=0),V.every((function(e){return e}))){D=M,P=!1;break}j.set(M,V)}if(P)for(var K=function(e){var t=x.find((function(t){var n=j.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return D=t,"break"},Q=E?3:1;Q>0&&"break"!==K(Q);Q--);t.placement!==D&&(t.modifiersData[i]._skip=!0,t.placement=D,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function be(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function we(e){return[r,a,o,s].some((function(t){return e[t]>=0}))}var ke={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,i=t.rects.reference,r=t.rects.popper,o=t.modifiersData.preventOverflow,a=_e(t,{elementContext:"reference"}),s=_e(t,{altBoundary:!0}),c=be(a,i),l=be(s,r,o),u=we(c),f=we(l);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:l,isReferenceHidden:u,hasPopperEscaped:f},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":f})}},Ee={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,i=e.name,o=n.offset,c=void 0===o?[0,0]:o,l=g.reduce((function(e,n){return e[n]=function(e,t,n){var i=I(e),o=[s,r].indexOf(i)>=0?-1:1,c="function"==typeof n?n(Object.assign({},t,{placement:e})):n,l=c[0],u=c[1];return l=l||0,u=(u||0)*o,[s,a].indexOf(i)>=0?{x:u,y:l}:{x:l,y:u}}(n,t.rects,c),e}),{}),u=l[t.placement],f=u.x,d=u.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=d),t.modifiersData[i]=l}},Ae={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=ge({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},Oe={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,i=e.name,c=n.mainAxis,l=void 0===c||c,f=n.altAxis,d=void 0!==f&&f,h=n.boundary,p=n.rootBoundary,v=n.altBoundary,m=n.padding,g=n.tether,_=void 0===g||g,y=n.tetherOffset,b=void 0===y?0:y,w=_e(t,{boundary:h,rootBoundary:p,padding:m,altBoundary:v}),k=I(t.placement),E=ee(t.placement),A=!E,O=Y(k),T="x"===O?"y":"x",C=t.modifiersData.popperOffsets,x=t.rects.reference,S=t.rects.popper,L="function"==typeof b?b(Object.assign({},t.rects,{placement:t.placement})):b,j="number"==typeof L?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),P=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,D={x:0,y:0};if(C){if(l){var F,H="y"===O?r:s,B="y"===O?o:a,W="y"===O?"height":"width",z=C[O],q=z+w[H],V=z-w[B],K=_?-S[W]/2:0,Q=E===u?x[W]:S[W],U=E===u?-S[W]:-x[W],G=t.elements.arrow,J=_&&G?R(G):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},te=Z[H],ne=Z[B],ie=$(0,x[W],J[W]),re=A?x[W]/2-K-ie-te-j.mainAxis:Q-ie-te-j.mainAxis,oe=A?-x[W]/2+K+ie+ne+j.mainAxis:U+ie+ne+j.mainAxis,ae=t.elements.arrow&&X(t.elements.arrow),se=ae?"y"===O?ae.clientTop||0:ae.clientLeft||0:0,ce=null!=(F=null==P?void 0:P[O])?F:0,le=z+oe-ce,ue=$(_?M(q,z+re-ce-se):q,z,_?N(V,le):V);C[O]=ue,D[O]=ue-z}if(d){var fe,de="x"===O?r:s,he="x"===O?o:a,pe=C[T],ve="y"===T?"height":"width",me=pe+w[de],ge=pe-w[he],ye=-1!==[r,s].indexOf(k),be=null!=(fe=null==P?void 0:P[T])?fe:0,we=ye?me:pe-x[ve]-S[ve]-be+j.altAxis,ke=ye?pe+x[ve]+S[ve]-be-j.altAxis:ge,Ee=_&&ye?function(e,t,n){var i=$(e,t,n);return i>n?n:i}(we,pe,ke):$(_?we:me,pe,_?ke:ge);C[T]=Ee,D[T]=Ee-pe}t.modifiersData[i]=D}},requiresIfExists:["offset"]};function Te(e,t,n){void 0===n&&(n=!1);var i,r,o=j(t),a=j(t)&&function(e){var t=e.getBoundingClientRect(),n=F(t.width)/e.offsetWidth||1,i=F(t.height)/e.offsetHeight||1;return 1!==n||1!==i}(t),s=K(t),c=W(e,a,n),l={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&(("body"!==x(t)||de(s))&&(l=(i=t)!==S(i)&&j(i)?{scrollLeft:(r=i).scrollLeft,scrollTop:r.scrollTop}:ue(i)),j(t)?((u=W(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):s&&(u.x=fe(s))),{x:c.left+l.scrollLeft-u.x,y:c.top+l.scrollTop-u.y,width:c.width,height:c.height}}function Ce(e){var t=new Map,n=new Set,i=[];function r(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var i=t.get(e);i&&r(i)}})),i.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||r(e)})),i}var xe={placement:"bottom",modifiers:[],strategy:"absolute"};function Se(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function Le(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,i=void 0===n?[]:n,r=t.defaultOptions,o=void 0===r?xe:r;return function(e,t,n){void 0===n&&(n=o);var r,a,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},xe,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},c=[],l=!1,u={state:s,setOptions:function(n){var r="function"==typeof n?n(s.options):n;f(),s.options=Object.assign({},o,s.options,r),s.scrollParents={reference:L(e)?pe(e):e.contextElement?pe(e.contextElement):[],popper:pe(t)};var a,l,d=function(e){var t=Ce(e);return C.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((a=[].concat(i,s.options.modifiers),l=a.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(l).map((function(e){return l[e]}))));return s.orderedModifiers=d.filter((function(e){return e.enabled})),s.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,i=void 0===n?{}:n,r=e.effect;if("function"==typeof r){var o=r({state:s,name:t,instance:u,options:i});c.push(o||function(){})}})),u.update()},forceUpdate:function(){if(!l){var e=s.elements,t=e.reference,n=e.popper;if(Se(t,n)){s.rects={reference:Te(t,X(n),"fixed"===s.options.strategy),popper:R(n)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach((function(e){return s.modifiersData[e.name]=Object.assign({},e.data)}));for(var i=0;i<s.orderedModifiers.length;i++)if(!0!==s.reset){var r=s.orderedModifiers[i],o=r.fn,a=r.options,c=void 0===a?{}:a,f=r.name;"function"==typeof o&&(s=o({state:s,options:c,name:f,instance:u})||s)}else s.reset=!1,i=-1}}},update:(r=function(){return new Promise((function(e){u.forceUpdate(),e(s)}))},function(){return a||(a=new Promise((function(e){Promise.resolve().then((function(){a=void 0,e(r())}))}))),a}),destroy:function(){f(),l=!0}};if(!Se(e,t))return u;function f(){c.forEach((function(e){return e()})),c=[]}return u.setOptions(n).then((function(e){!l&&n.onFirstUpdate&&n.onFirstUpdate(e)})),u}}var je=Le(),Pe=Le({defaultModifiers:[oe,Ae,ie,D,Ee,ye,Oe,Z,ke]}),De=Le({defaultModifiers:[oe,Ae,ie,D]});function Ie(){return Ie="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=He(e)););return e}(e,t);if(i){var r=Object.getOwnPropertyDescriptor(i,t);return r.get?r.get.call(arguments.length<3?e:n):r.value}},Ie.apply(this,arguments)}function Ne(e,t,n){return t=He(t),Me(e,Fe()?Reflect.construct(t,n||[],He(e).constructor):t.apply(e,n))}function Me(e,t){if(t&&("object"===et(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Be(e)}function Fe(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Fe=function(){return!!e})()}function He(e){return He=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},He(e)}function Be(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function We(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Re(e,t)}function Re(e,t){return Re=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Re(e,t)}function ze(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function qe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ze(Object(n),!0).forEach((function(t){Ve(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ze(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ve(e,t,n){return(t=Xe(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ke(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qe(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Xe(i.key),i)}}function Ue(e,t,n){return t&&Qe(e.prototype,t),n&&Qe(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Xe(e){var t=function(e,t){if("object"!=et(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,"string");if("object"!=et(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==et(t)?t:String(t)}function Ye(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(i=o.call(n)).done)&&(s.push(i.value),s.length!==t);c=!0);}catch(e){l=!0,r=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw r}}return s}}(e,t)||Je(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $e(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Je(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){s=!0,o=e},f:function(){try{a||null==n.return||n.return()}finally{if(s)throw o}}}}function Ge(e){return function(e){if(Array.isArray(e))return Ze(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Je(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Je(e,t){if(e){if("string"==typeof e)return Ze(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ze(e,t):void 0}}function Ze(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function et(e){return et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},et(e)}var tt=new Map,nt="transitionend",it=function(e){return e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(function(e,t){return"#".concat(CSS.escape(t))}))),e},rt=function(e){e.dispatchEvent(new Event(nt))},ot=function(e){return!(!e||"object"!==et(e))&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType)},at=function(e){return ot(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(it(e)):null},st=function(e){if(!ot(e)||0===e.getClientRects().length)return!1;var t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){var i=e.closest("summary");if(i&&i.parentNode!==n)return!1;if(null===i)return!1}return t},ct=function(e){return!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled"))},lt=function e(t){if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){var n=t.getRootNode();return n instanceof ShadowRoot?n:null}return t instanceof ShadowRoot?t:t.parentNode?e(t.parentNode):null},ut=function(){},ft=function(e){e.offsetHeight},dt=function(){return window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null},ht=[],pt=function(){return"rtl"===document.documentElement.dir},vt=function(e){var t;t=function(){var t=dt();if(t){var n=e.NAME,i=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=function(){return t.fn[n]=i,e.jQueryInterface}}},"loading"===document.readyState?(ht.length||document.addEventListener("DOMContentLoaded",(function(){for(var e=0,t=ht;e<t.length;e++)(0,t[e])()})),ht.push(t)):t()},mt=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e;return"function"==typeof e?e.apply(void 0,Ge(t)):n},gt=function(e,t){if(arguments.length>2&&void 0!==arguments[2]&&!arguments[2])mt(e);else{var n=function(e){if(!e)return 0;var t=window.getComputedStyle(e),n=t.transitionDuration,i=t.transitionDelay,r=Number.parseFloat(n),o=Number.parseFloat(i);return r||o?(n=n.split(",")[0],i=i.split(",")[0],1e3*(Number.parseFloat(n)+Number.parseFloat(i))):0}(t)+5,i=!1;t.addEventListener(nt,(function n(r){r.target===t&&(i=!0,t.removeEventListener(nt,n),mt(e))})),setTimeout((function(){i||rt(t)}),n)}},_t=function(e,t,n,i){var r=e.length,o=e.indexOf(t);return-1===o?!n&&i?e[r-1]:e[0]:(o+=n?1:-1,i&&(o=(o+r)%r),e[Math.max(0,Math.min(o,r-1))])},yt=/[^.]*(?=\..*)\.|.*/,bt=/\..*/,wt=/::\d+$/,kt={},Et=1,At={mouseenter:"mouseover",mouseleave:"mouseout"},Ot=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Tt(e,t){return t&&"".concat(t,"::").concat(Et++)||e.uidEvent||Et++}function Ct(e){var t=Tt(e);return e.uidEvent=t,kt[t]=kt[t]||{},kt[t]}function xt(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return Object.values(e).find((function(e){return e.callable===t&&e.delegationSelector===n}))}function St(e,t,n){var i="string"==typeof t,r=i?n:t||n,o=Dt(e);return Ot.has(o)||(o=e),[i,r,o]}function Lt(e,t,n,i,r){if("string"==typeof t&&e){var o=Ye(St(t,n,i),3),a=o[0],s=o[1],c=o[2];t in At&&(s=function(e){return function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)}}(s));var l=Ct(e),u=l[c]||(l[c]={}),f=xt(u,s,a?n:null);if(f)f.oneOff=f.oneOff&&r;else{var d=Tt(s,t.replace(yt,"")),h=a?function(e,t,n){return function i(r){for(var o=e.querySelectorAll(t),a=r.target;a&&a!==this;a=a.parentNode){var s,c=$e(o);try{for(c.s();!(s=c.n()).done;)if(s.value===a)return Nt(r,{delegateTarget:a}),i.oneOff&&It.off(e,r.type,t,n),n.apply(a,[r])}catch(e){c.e(e)}finally{c.f()}}}}(e,n,s):function(e,t){return function n(i){return Nt(i,{delegateTarget:e}),n.oneOff&&It.off(e,i.type,t),t.apply(e,[i])}}(e,s);h.delegationSelector=a?n:null,h.callable=s,h.oneOff=r,h.uidEvent=d,u[d]=h,e.addEventListener(c,h,a)}}}function jt(e,t,n,i,r){var o=xt(t[n],i,r);o&&(e.removeEventListener(n,o,Boolean(r)),delete t[n][o.uidEvent])}function Pt(e,t,n,i){for(var r=t[n]||{},o=0,a=Object.entries(r);o<a.length;o++){var s=Ye(a[o],2),c=s[0],l=s[1];c.includes(i)&&jt(e,t,n,l.callable,l.delegationSelector)}}function Dt(e){return e=e.replace(bt,""),At[e]||e}var It={on:function(e,t,n,i){Lt(e,t,n,i,!1)},one:function(e,t,n,i){Lt(e,t,n,i,!0)},off:function(e,t,n,i){if("string"==typeof t&&e){var r=Ye(St(t,n,i),3),o=r[0],a=r[1],s=r[2],c=s!==t,l=Ct(e),u=l[s]||{},f=t.startsWith(".");if(void 0===a){if(f)for(var d=0,h=Object.keys(l);d<h.length;d++)Pt(e,l,h[d],t.slice(1));for(var p=0,v=Object.entries(u);p<v.length;p++){var m=Ye(v[p],2),g=m[0],_=m[1],y=g.replace(wt,"");c&&!t.includes(y)||jt(e,l,s,_.callable,_.delegationSelector)}}else{if(!Object.keys(u).length)return;jt(e,l,s,a,o?n:null)}}},trigger:function(e,t,n){if("string"!=typeof t||!e)return null;var i=dt(),r=null,o=!0,a=!0,s=!1;t!==Dt(t)&&i&&(r=i.Event(t,n),i(e).trigger(r),o=!r.isPropagationStopped(),a=!r.isImmediatePropagationStopped(),s=r.isDefaultPrevented());var c=Nt(new Event(t,{bubbles:o,cancelable:!0}),n);return s&&c.preventDefault(),a&&e.dispatchEvent(c),c.defaultPrevented&&r&&r.preventDefault(),c}};function Nt(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=function(){var n=o[r],i=(t=Ye(n,2))[0],a=t[1];try{e[i]=a}catch(t){Object.defineProperty(e,i,{configurable:!0,get:function(){return a}})}},r=0,o=Object.entries(n);r<o.length;r++)i();return e}function Mt(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function Ft(e){return e.replace(/[A-Z]/g,(function(e){return"-".concat(e.toLowerCase())}))}var Ht=function(e,t,n){e.setAttribute("data-bs-".concat(Ft(t)),n)},Bt=function(e,t){e.removeAttribute("data-bs-".concat(Ft(t)))},Wt=function(e){if(!e)return{};var t,n={},i=Object.keys(e.dataset).filter((function(e){return e.startsWith("bs")&&!e.startsWith("bsConfig")})),r=$e(i);try{for(r.s();!(t=r.n()).done;){var o=t.value,a=o.replace(/^bs/,"");n[a=a.charAt(0).toLowerCase()+a.slice(1,a.length)]=Mt(e.dataset[o])}}catch(e){r.e(e)}finally{r.f()}return n},Rt=function(e,t){return Mt(e.getAttribute("data-bs-".concat(Ft(t))))},zt=function(){function e(){Ke(this,e)}return Ue(e,[{key:"_getConfig",value:function(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}},{key:"_configAfterMerge",value:function(e){return e}},{key:"_mergeConfigObj",value:function(e,t){var n=ot(t)?Rt(t,"config"):{};return qe(qe(qe(qe({},this.constructor.Default),"object"===et(n)?n:{}),ot(t)?Wt(t):{}),"object"===et(e)?e:{})}},{key:"_typeCheckConfig",value:function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.constructor.DefaultType,i=0,r=Object.entries(n);i<r.length;i++){var o=Ye(r[i],2),a=o[0],s=o[1],c=e[a],l=ot(c)?"element":null==(t=c)?"".concat(t):Object.prototype.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(l))throw new TypeError("".concat(this.constructor.NAME.toUpperCase(),': Option "').concat(a,'" provided type "').concat(l,'" but expected type "').concat(s,'".'))}}}],[{key:"Default",get:function(){return{}}},{key:"DefaultType",get:function(){return{}}},{key:"NAME",get:function(){throw new Error('You have to implement the static method "NAME", for each component!')}}]),e}(),qt=function(e){function t(e,n){var i;return Ke(this,t),i=Ne(this,t),(e=at(e))?(i._element=e,i._config=i._getConfig(n),function(e,t,n){tt.has(e)||tt.set(e,new Map);var i=tt.get(e);i.has(t)||0===i.size?i.set(t,n):console.error("Bootstrap doesn't allow more than one instance per element. Bound instance: ".concat(Array.from(i.keys())[0],"."))}(i._element,i.constructor.DATA_KEY,Be(i)),i):Me(i)}return We(t,e),Ue(t,[{key:"dispose",value:function(){(function(e,t){if(tt.has(e)){var n=tt.get(e);n.delete(t),0===n.size&&tt.delete(e)}})(this._element,this.constructor.DATA_KEY),It.off(this._element,this.constructor.EVENT_KEY);var e,t=$e(Object.getOwnPropertyNames(this));try{for(t.s();!(e=t.n()).done;)this[e.value]=null}catch(e){t.e(e)}finally{t.f()}}},{key:"_queueCallback",value:function(e,t){gt(e,t,!(arguments.length>2&&void 0!==arguments[2])||arguments[2])}},{key:"_getConfig",value:function(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}}],[{key:"getInstance",value:function(e){return function(e,t){return tt.has(e)&&tt.get(e).get(t)||null}(at(e),this.DATA_KEY)}},{key:"getOrCreateInstance",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.getInstance(e)||new this(e,"object"===et(t)?t:null)}},{key:"VERSION",get:function(){return"5.3.3"}},{key:"DATA_KEY",get:function(){return"bs.".concat(this.NAME)}},{key:"EVENT_KEY",get:function(){return".".concat(this.DATA_KEY)}},{key:"eventName",value:function(e){return"".concat(e).concat(this.EVENT_KEY)}}]),t}(zt),Vt=function(e){var t=e.getAttribute("data-bs-target");if(!t||"#"===t){var n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n="#".concat(n.split("#")[1])),t=n&&"#"!==n?n.trim():null}return t?t.split(",").map((function(e){return it(e)})).join(","):null},Kt={find:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.documentElement;return(t=[]).concat.apply(t,Ge(Element.prototype.querySelectorAll.call(n,e)))},findOne:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.documentElement;return Element.prototype.querySelector.call(t,e)},children:function(e,t){var n;return(n=[]).concat.apply(n,Ge(e.children)).filter((function(e){return e.matches(t)}))},parents:function(e,t){for(var n=[],i=e.parentNode.closest(t);i;)n.push(i),i=i.parentNode.closest(t);return n},prev:function(e,t){for(var n=e.previousElementSibling;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next:function(e,t){for(var n=e.nextElementSibling;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren:function(e){var t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((function(e){return"".concat(e,':not([tabindex^="-"])')})).join(",");return this.find(t,e).filter((function(e){return!ct(e)&&st(e)}))},getSelectorFromElement:function(e){var t=Vt(e);return t&&Kt.findOne(t)?t:null},getElementFromSelector:function(e){var t=Vt(e);return t?Kt.findOne(t):null},getMultipleElementsFromSelector:function(e){var t=Vt(e);return t?Kt.find(t):[]}},Qt=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"hide",n="click.dismiss".concat(e.EVENT_KEY),i=e.NAME;It.on(document,n,'[data-bs-dismiss="'.concat(i,'"]'),(function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),!ct(this)){var r=Kt.getElementFromSelector(this)||this.closest(".".concat(i));e.getOrCreateInstance(r)[t]()}}))},Ut=".".concat("bs.alert"),Xt="close".concat(Ut),Yt="closed".concat(Ut),$t=function(e){function t(){return Ke(this,t),Ne(this,t,arguments)}return We(t,e),Ue(t,[{key:"close",value:function(){var e=this;if(!It.trigger(this._element,Xt).defaultPrevented){this._element.classList.remove("show");var t=this._element.classList.contains("fade");this._queueCallback((function(){return e._destroyElement()}),this._element,t)}}},{key:"_destroyElement",value:function(){this._element.remove(),It.trigger(this._element,Yt),this.dispose()}}],[{key:"NAME",get:function(){return"alert"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this);if("string"==typeof e){if(void 0===n[e]||e.startsWith("_")||"constructor"===e)throw new TypeError('No method named "'.concat(e,'"'));n[e](this)}}))}}]),t}(qt);Qt($t,"close"),vt($t);var Gt=".".concat("bs.button"),Jt='[data-bs-toggle="button"]',Zt="click".concat(Gt).concat(".data-api"),en=function(e){function t(){return Ke(this,t),Ne(this,t,arguments)}return We(t,e),Ue(t,[{key:"toggle",value:function(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}}],[{key:"NAME",get:function(){return"button"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this);"toggle"===e&&n[e]()}))}}]),t}(qt);It.on(document,Zt,Jt,(function(e){e.preventDefault();var t=e.target.closest(Jt);en.getOrCreateInstance(t).toggle()})),vt(en);var tn=".bs.swipe",nn="touchstart".concat(tn),rn="touchmove".concat(tn),on="touchend".concat(tn),an="pointerdown".concat(tn),sn="pointerup".concat(tn),cn={endCallback:null,leftCallback:null,rightCallback:null},ln={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"},un=function(e){function t(e,n){var i;return Ke(this,t),(i=Ne(this,t))._element=e,e&&t.isSupported()?(i._config=i._getConfig(n),i._deltaX=0,i._supportPointerEvents=Boolean(window.PointerEvent),i._initEvents(),i):Me(i)}return We(t,e),Ue(t,[{key:"dispose",value:function(){It.off(this._element,tn)}},{key:"_start",value:function(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}},{key:"_end",value:function(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),mt(this._config.endCallback)}},{key:"_move",value:function(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}},{key:"_handleSwipe",value:function(){var e=Math.abs(this._deltaX);if(!(e<=40)){var t=e/this._deltaX;this._deltaX=0,t&&mt(t>0?this._config.rightCallback:this._config.leftCallback)}}},{key:"_initEvents",value:function(){var e=this;this._supportPointerEvents?(It.on(this._element,an,(function(t){return e._start(t)})),It.on(this._element,sn,(function(t){return e._end(t)})),this._element.classList.add("pointer-event")):(It.on(this._element,nn,(function(t){return e._start(t)})),It.on(this._element,rn,(function(t){return e._move(t)})),It.on(this._element,on,(function(t){return e._end(t)})))}},{key:"_eventIsPointerPenTouch",value:function(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}}],[{key:"Default",get:function(){return cn}},{key:"DefaultType",get:function(){return ln}},{key:"NAME",get:function(){return"swipe"}},{key:"isSupported",value:function(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}]),t}(zt),fn=".".concat("bs.carousel"),dn=".data-api",hn="next",pn="prev",vn="left",mn="right",gn="slide".concat(fn),_n="slid".concat(fn),yn="keydown".concat(fn),bn="mouseenter".concat(fn),wn="mouseleave".concat(fn),kn="dragstart".concat(fn),En="load".concat(fn).concat(dn),An="click".concat(fn).concat(dn),On="carousel",Tn="active",Cn=".active",xn=".carousel-item",Sn=Cn+xn,Ln=Ve(Ve({},"ArrowLeft",mn),"ArrowRight",vn),jn={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Pn={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"},Dn=function(e){function t(e,n){var i;return Ke(this,t),(i=Ne(this,t,[e,n]))._interval=null,i._activeElement=null,i._isSliding=!1,i.touchTimeout=null,i._swipeHelper=null,i._indicatorsElement=Kt.findOne(".carousel-indicators",i._element),i._addEventListeners(),i._config.ride===On&&i.cycle(),i}return We(t,e),Ue(t,[{key:"next",value:function(){this._slide(hn)}},{key:"nextWhenVisible",value:function(){!document.hidden&&st(this._element)&&this.next()}},{key:"prev",value:function(){this._slide(pn)}},{key:"pause",value:function(){this._isSliding&&rt(this._element),this._clearInterval()}},{key:"cycle",value:function(){var e=this;this._clearInterval(),this._updateInterval(),this._interval=setInterval((function(){return e.nextWhenVisible()}),this._config.interval)}},{key:"_maybeEnableCycle",value:function(){var e=this;this._config.ride&&(this._isSliding?It.one(this._element,_n,(function(){return e.cycle()})):this.cycle())}},{key:"to",value:function(e){var t=this,n=this._getItems();if(!(e>n.length-1||e<0))if(this._isSliding)It.one(this._element,_n,(function(){return t.to(e)}));else{var i=this._getItemIndex(this._getActive());if(i!==e){var r=e>i?hn:pn;this._slide(r,n[e])}}}},{key:"dispose",value:function(){this._swipeHelper&&this._swipeHelper.dispose(),Ie(He(t.prototype),"dispose",this).call(this)}},{key:"_configAfterMerge",value:function(e){return e.defaultInterval=e.interval,e}},{key:"_addEventListeners",value:function(){var e=this;this._config.keyboard&&It.on(this._element,yn,(function(t){return e._keydown(t)})),"hover"===this._config.pause&&(It.on(this._element,bn,(function(){return e.pause()})),It.on(this._element,wn,(function(){return e._maybeEnableCycle()}))),this._config.touch&&un.isSupported()&&this._addTouchEventListeners()}},{key:"_addTouchEventListeners",value:function(){var e,t=this,n=$e(Kt.find(".carousel-item img",this._element));try{for(n.s();!(e=n.n()).done;){var i=e.value;It.on(i,kn,(function(e){return e.preventDefault()}))}}catch(e){n.e(e)}finally{n.f()}var r={leftCallback:function(){return t._slide(t._directionToOrder(vn))},rightCallback:function(){return t._slide(t._directionToOrder(mn))},endCallback:function(){"hover"===t._config.pause&&(t.pause(),t.touchTimeout&&clearTimeout(t.touchTimeout),t.touchTimeout=setTimeout((function(){return t._maybeEnableCycle()}),500+t._config.interval))}};this._swipeHelper=new un(this._element,r)}},{key:"_keydown",value:function(e){if(!/input|textarea/i.test(e.target.tagName)){var t=Ln[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}}},{key:"_getItemIndex",value:function(e){return this._getItems().indexOf(e)}},{key:"_setActiveIndicatorElement",value:function(e){if(this._indicatorsElement){var t=Kt.findOne(Cn,this._indicatorsElement);t.classList.remove(Tn),t.removeAttribute("aria-current");var n=Kt.findOne('[data-bs-slide-to="'.concat(e,'"]'),this._indicatorsElement);n&&(n.classList.add(Tn),n.setAttribute("aria-current","true"))}}},{key:"_updateInterval",value:function(){var e=this._activeElement||this._getActive();if(e){var t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}}},{key:"_slide",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!this._isSliding){var i=this._getActive(),r=e===hn,o=n||_t(this._getItems(),i,r,this._config.wrap);if(o!==i){var a=this._getItemIndex(o),s=function(n){return It.trigger(t._element,n,{relatedTarget:o,direction:t._orderToDirection(e),from:t._getItemIndex(i),to:a})};if(!s(gn).defaultPrevented&&i&&o){var c=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(a),this._activeElement=o;var l=r?"carousel-item-start":"carousel-item-end",u=r?"carousel-item-next":"carousel-item-prev";o.classList.add(u),ft(o),i.classList.add(l),o.classList.add(l),this._queueCallback((function(){o.classList.remove(l,u),o.classList.add(Tn),i.classList.remove(Tn,u,l),t._isSliding=!1,s(_n)}),i,this._isAnimated()),c&&this.cycle()}}}}},{key:"_isAnimated",value:function(){return this._element.classList.contains("slide")}},{key:"_getActive",value:function(){return Kt.findOne(Sn,this._element)}},{key:"_getItems",value:function(){return Kt.find(xn,this._element)}},{key:"_clearInterval",value:function(){this._interval&&(clearInterval(this._interval),this._interval=null)}},{key:"_directionToOrder",value:function(e){return pt()?e===vn?pn:hn:e===vn?hn:pn}},{key:"_orderToDirection",value:function(e){return pt()?e===pn?vn:mn:e===pn?mn:vn}}],[{key:"Default",get:function(){return jn}},{key:"DefaultType",get:function(){return Pn}},{key:"NAME",get:function(){return"carousel"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===n[e]||e.startsWith("_")||"constructor"===e)throw new TypeError('No method named "'.concat(e,'"'));n[e]()}}else n.to(e)}))}}]),t}(qt);It.on(document,An,"[data-bs-slide], [data-bs-slide-to]",(function(e){var t=Kt.getElementFromSelector(this);if(t&&t.classList.contains(On)){e.preventDefault();var n=Dn.getOrCreateInstance(t),i=this.getAttribute("data-bs-slide-to");if(i)return n.to(i),void n._maybeEnableCycle();if("next"===Rt(this,"slide"))return n.next(),void n._maybeEnableCycle();n.prev(),n._maybeEnableCycle()}})),It.on(window,En,(function(){var e,t=$e(Kt.find('[data-bs-ride="carousel"]'));try{for(t.s();!(e=t.n()).done;){var n=e.value;Dn.getOrCreateInstance(n)}}catch(e){t.e(e)}finally{t.f()}})),vt(Dn);var In=".".concat("bs.collapse"),Nn="show".concat(In),Mn="shown".concat(In),Fn="hide".concat(In),Hn="hidden".concat(In),Bn="click".concat(In).concat(".data-api"),Wn="show",Rn="collapse",zn="collapsing",qn=":scope .".concat(Rn," .").concat(Rn),Vn='[data-bs-toggle="collapse"]',Kn={parent:null,toggle:!0},Qn={parent:"(null|element)",toggle:"boolean"},Un=function(e){function t(e,n){var i;Ke(this,t),(i=Ne(this,t,[e,n]))._isTransitioning=!1,i._triggerArray=[];var r,o=$e(Kt.find(Vn));try{for(o.s();!(r=o.n()).done;){var a=r.value,s=Kt.getSelectorFromElement(a),c=Kt.find(s).filter((function(e){return e===i._element}));null!==s&&c.length&&i._triggerArray.push(a)}}catch(e){o.e(e)}finally{o.f()}return i._initializeChildren(),i._config.parent||i._addAriaAndCollapsedClass(i._triggerArray,i._isShown()),i._config.toggle&&i.toggle(),i}return We(t,e),Ue(t,[{key:"toggle",value:function(){this._isShown()?this.hide():this.show()}},{key:"show",value:function(){var e=this;if(!this._isTransitioning&&!this._isShown()){var n=[];if(!(this._config.parent&&(n=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((function(t){return t!==e._element})).map((function(e){return t.getOrCreateInstance(e,{toggle:!1})}))),n.length&&n[0]._isTransitioning||It.trigger(this._element,Nn).defaultPrevented)){var i,r=$e(n);try{for(r.s();!(i=r.n()).done;)i.value.hide()}catch(e){r.e(e)}finally{r.f()}var o=this._getDimension();this._element.classList.remove(Rn),this._element.classList.add(zn),this._element.style[o]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;var a=o[0].toUpperCase()+o.slice(1),s="scroll".concat(a);this._queueCallback((function(){e._isTransitioning=!1,e._element.classList.remove(zn),e._element.classList.add(Rn,Wn),e._element.style[o]="",It.trigger(e._element,Mn)}),this._element,!0),this._element.style[o]="".concat(this._element[s],"px")}}}},{key:"hide",value:function(){var e=this;if(!this._isTransitioning&&this._isShown()&&!It.trigger(this._element,Fn).defaultPrevented){var t=this._getDimension();this._element.style[t]="".concat(this._element.getBoundingClientRect()[t],"px"),ft(this._element),this._element.classList.add(zn),this._element.classList.remove(Rn,Wn);var n,i=$e(this._triggerArray);try{for(i.s();!(n=i.n()).done;){var r=n.value,o=Kt.getElementFromSelector(r);o&&!this._isShown(o)&&this._addAriaAndCollapsedClass([r],!1)}}catch(e){i.e(e)}finally{i.f()}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback((function(){e._isTransitioning=!1,e._element.classList.remove(zn),e._element.classList.add(Rn),It.trigger(e._element,Hn)}),this._element,!0)}}},{key:"_isShown",value:function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._element).classList.contains(Wn)}},{key:"_configAfterMerge",value:function(e){return e.toggle=Boolean(e.toggle),e.parent=at(e.parent),e}},{key:"_getDimension",value:function(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}},{key:"_initializeChildren",value:function(){if(this._config.parent){var e,t=$e(this._getFirstLevelChildren(Vn));try{for(t.s();!(e=t.n()).done;){var n=e.value,i=Kt.getElementFromSelector(n);i&&this._addAriaAndCollapsedClass([n],this._isShown(i))}}catch(e){t.e(e)}finally{t.f()}}}},{key:"_getFirstLevelChildren",value:function(e){var t=Kt.find(qn,this._config.parent);return Kt.find(e,this._config.parent).filter((function(e){return!t.includes(e)}))}},{key:"_addAriaAndCollapsedClass",value:function(e,t){if(e.length){var n,i=$e(e);try{for(i.s();!(n=i.n()).done;){var r=n.value;r.classList.toggle("collapsed",!t),r.setAttribute("aria-expanded",t)}}catch(e){i.e(e)}finally{i.f()}}}}],[{key:"Default",get:function(){return Kn}},{key:"DefaultType",get:function(){return Qn}},{key:"NAME",get:function(){return"collapse"}},{key:"jQueryInterface",value:function(e){var n={};return"string"==typeof e&&/show|hide/.test(e)&&(n.toggle=!1),this.each((function(){var i=t.getOrCreateInstance(this,n);if("string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'.concat(e,'"'));i[e]()}}))}}]),t}(qt);It.on(document,Bn,Vn,(function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();var t,n=$e(Kt.getMultipleElementsFromSelector(this));try{for(n.s();!(t=n.n()).done;){var i=t.value;Un.getOrCreateInstance(i,{toggle:!1}).toggle()}}catch(e){n.e(e)}finally{n.f()}})),vt(Un);var Xn="dropdown",Yn=".".concat("bs.dropdown"),$n=".data-api",Gn="ArrowUp",Jn="ArrowDown",Zn="hide".concat(Yn),ei="hidden".concat(Yn),ti="show".concat(Yn),ni="shown".concat(Yn),ii="click".concat(Yn).concat($n),ri="keydown".concat(Yn).concat($n),oi="keyup".concat(Yn).concat($n),ai="show",si='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',ci="".concat(si,".").concat(ai),li=".dropdown-menu",ui=pt()?"top-end":"top-start",fi=pt()?"top-start":"top-end",di=pt()?"bottom-end":"bottom-start",hi=pt()?"bottom-start":"bottom-end",pi=pt()?"left-start":"right-start",vi=pt()?"right-start":"left-start",mi={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},gi={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"},_i=function(e){function t(e,n){var i;return Ke(this,t),(i=Ne(this,t,[e,n]))._popper=null,i._parent=i._element.parentNode,i._menu=Kt.next(i._element,li)[0]||Kt.prev(i._element,li)[0]||Kt.findOne(li,i._parent),i._inNavbar=i._detectNavbar(),i}return We(t,e),Ue(t,[{key:"toggle",value:function(){return this._isShown()?this.hide():this.show()}},{key:"show",value:function(){if(!ct(this._element)&&!this._isShown()){var e={relatedTarget:this._element};if(!It.trigger(this._element,ti,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav")){var t,n,i=$e((t=[]).concat.apply(t,Ge(document.body.children)));try{for(i.s();!(n=i.n()).done;){var r=n.value;It.on(r,"mouseover",ut)}}catch(e){i.e(e)}finally{i.f()}}this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(ai),this._element.classList.add(ai),It.trigger(this._element,ni,e)}}}},{key:"hide",value:function(){if(!ct(this._element)&&this._isShown()){var e={relatedTarget:this._element};this._completeHide(e)}}},{key:"dispose",value:function(){this._popper&&this._popper.destroy(),Ie(He(t.prototype),"dispose",this).call(this)}},{key:"update",value:function(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}},{key:"_completeHide",value:function(e){if(!It.trigger(this._element,Zn,e).defaultPrevented){if("ontouchstart"in document.documentElement){var t,n,i=$e((t=[]).concat.apply(t,Ge(document.body.children)));try{for(i.s();!(n=i.n()).done;){var r=n.value;It.off(r,"mouseover",ut)}}catch(e){i.e(e)}finally{i.f()}}this._popper&&this._popper.destroy(),this._menu.classList.remove(ai),this._element.classList.remove(ai),this._element.setAttribute("aria-expanded","false"),Bt(this._menu,"popper"),It.trigger(this._element,ei,e)}}},{key:"_getConfig",value:function(e){if("object"===et((e=Ie(He(t.prototype),"_getConfig",this).call(this,e)).reference)&&!ot(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw new TypeError("".concat(Xn.toUpperCase(),': Option "reference" provided type "object" without a required "getBoundingClientRect" method.'));return e}},{key:"_createPopper",value:function(){if(void 0===n)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");var e=this._element;"parent"===this._config.reference?e=this._parent:ot(this._config.reference)?e=at(this._config.reference):"object"===et(this._config.reference)&&(e=this._config.reference);var t=this._getPopperConfig();this._popper=Pe(e,this._menu,t)}},{key:"_isShown",value:function(){return this._menu.classList.contains(ai)}},{key:"_getPlacement",value:function(){var e=this._parent;if(e.classList.contains("dropend"))return pi;if(e.classList.contains("dropstart"))return vi;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";var t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?fi:ui:t?hi:di}},{key:"_detectNavbar",value:function(){return null!==this._element.closest(".navbar")}},{key:"_getOffset",value:function(){var e=this,t=this._config.offset;return"string"==typeof t?t.split(",").map((function(e){return Number.parseInt(e,10)})):"function"==typeof t?function(n){return t(n,e._element)}:t}},{key:"_getPopperConfig",value:function(){var e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(Ht(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),qe(qe({},e),mt(this._config.popperConfig,[e]))}},{key:"_selectMenuItem",value:function(e){var t=e.key,n=e.target,i=Kt.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((function(e){return st(e)}));i.length&&_t(i,n,t===Jn,!i.includes(n)).focus()}}],[{key:"Default",get:function(){return mi}},{key:"DefaultType",get:function(){return gi}},{key:"NAME",get:function(){return Xn}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'.concat(e,'"'));n[e]()}}))}},{key:"clearMenus",value:function(e){if(2!==e.button&&("keyup"!==e.type||"Tab"===e.key)){var n,i=$e(Kt.find(ci));try{for(i.s();!(n=i.n()).done;){var r=n.value,o=t.getInstance(r);if(o&&!1!==o._config.autoClose){var a=e.composedPath(),s=a.includes(o._menu);if(!(a.includes(o._element)||"inside"===o._config.autoClose&&!s||"outside"===o._config.autoClose&&s||o._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))){var c={relatedTarget:o._element};"click"===e.type&&(c.clickEvent=e),o._completeHide(c)}}}}catch(e){i.e(e)}finally{i.f()}}}},{key:"dataApiKeydownHandler",value:function(e){var n=/input|textarea/i.test(e.target.tagName),i="Escape"===e.key,r=[Gn,Jn].includes(e.key);if((r||i)&&(!n||i)){e.preventDefault();var o=this.matches(si)?this:Kt.prev(this,si)[0]||Kt.next(this,si)[0]||Kt.findOne(si,e.delegateTarget.parentNode),a=t.getOrCreateInstance(o);if(r)return e.stopPropagation(),a.show(),void a._selectMenuItem(e);a._isShown()&&(e.stopPropagation(),a.hide(),o.focus())}}}]),t}(qt);It.on(document,ri,si,_i.dataApiKeydownHandler),It.on(document,ri,li,_i.dataApiKeydownHandler),It.on(document,ii,_i.clearMenus),It.on(document,oi,_i.clearMenus),It.on(document,ii,si,(function(e){e.preventDefault(),_i.getOrCreateInstance(this).toggle()})),vt(_i);var yi="backdrop",bi="show",wi="mousedown.bs.".concat(yi),ki={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Ei={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"},Ai=function(e){function t(e){var n;return Ke(this,t),(n=Ne(this,t))._config=n._getConfig(e),n._isAppended=!1,n._element=null,n}return We(t,e),Ue(t,[{key:"show",value:function(e){if(this._config.isVisible){this._append();var t=this._getElement();this._config.isAnimated&&ft(t),t.classList.add(bi),this._emulateAnimation((function(){mt(e)}))}else mt(e)}},{key:"hide",value:function(e){var t=this;this._config.isVisible?(this._getElement().classList.remove(bi),this._emulateAnimation((function(){t.dispose(),mt(e)}))):mt(e)}},{key:"dispose",value:function(){this._isAppended&&(It.off(this._element,wi),this._element.remove(),this._isAppended=!1)}},{key:"_getElement",value:function(){if(!this._element){var e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}},{key:"_configAfterMerge",value:function(e){return e.rootElement=at(e.rootElement),e}},{key:"_append",value:function(){var e=this;if(!this._isAppended){var t=this._getElement();this._config.rootElement.append(t),It.on(t,wi,(function(){mt(e._config.clickCallback)})),this._isAppended=!0}}},{key:"_emulateAnimation",value:function(e){gt(e,this._getElement(),this._config.isAnimated)}}],[{key:"Default",get:function(){return ki}},{key:"DefaultType",get:function(){return Ei}},{key:"NAME",get:function(){return yi}}]),t}(zt),Oi=".".concat("bs.focustrap"),Ti="focusin".concat(Oi),Ci="keydown.tab".concat(Oi),xi="backward",Si={autofocus:!0,trapElement:null},Li={autofocus:"boolean",trapElement:"element"},ji=function(e){function t(e){var n;return Ke(this,t),(n=Ne(this,t))._config=n._getConfig(e),n._isActive=!1,n._lastTabNavDirection=null,n}return We(t,e),Ue(t,[{key:"activate",value:function(){var e=this;this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),It.off(document,Oi),It.on(document,Ti,(function(t){return e._handleFocusin(t)})),It.on(document,Ci,(function(t){return e._handleKeydown(t)})),this._isActive=!0)}},{key:"deactivate",value:function(){this._isActive&&(this._isActive=!1,It.off(document,Oi))}},{key:"_handleFocusin",value:function(e){var t=this._config.trapElement;if(e.target!==document&&e.target!==t&&!t.contains(e.target)){var n=Kt.focusableChildren(t);0===n.length?t.focus():this._lastTabNavDirection===xi?n[n.length-1].focus():n[0].focus()}}},{key:"_handleKeydown",value:function(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?xi:"forward")}}],[{key:"Default",get:function(){return Si}},{key:"DefaultType",get:function(){return Li}},{key:"NAME",get:function(){return"focustrap"}}]),t}(zt),Pi=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Di=".sticky-top",Ii="padding-right",Ni="margin-right",Mi=function(){function e(){Ke(this,e),this._element=document.body}return Ue(e,[{key:"getWidth",value:function(){var e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}},{key:"hide",value:function(){var e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,Ii,(function(t){return t+e})),this._setElementAttributes(Pi,Ii,(function(t){return t+e})),this._setElementAttributes(Di,Ni,(function(t){return t-e}))}},{key:"reset",value:function(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,Ii),this._resetElementAttributes(Pi,Ii),this._resetElementAttributes(Di,Ni)}},{key:"isOverflowing",value:function(){return this.getWidth()>0}},{key:"_disableOverFlow",value:function(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}},{key:"_setElementAttributes",value:function(e,t,n){var i=this,r=this.getWidth();this._applyManipulationCallback(e,(function(e){if(!(e!==i._element&&window.innerWidth>e.clientWidth+r)){i._saveInitialAttribute(e,t);var o=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,"".concat(n(Number.parseFloat(o)),"px"))}}))}},{key:"_saveInitialAttribute",value:function(e,t){var n=e.style.getPropertyValue(t);n&&Ht(e,t,n)}},{key:"_resetElementAttributes",value:function(e,t){this._applyManipulationCallback(e,(function(e){var n=Rt(e,t);null!==n?(Bt(e,t),e.style.setProperty(t,n)):e.style.removeProperty(t)}))}},{key:"_applyManipulationCallback",value:function(e,t){if(ot(e))t(e);else{var n,i=$e(Kt.find(e,this._element));try{for(i.s();!(n=i.n()).done;)t(n.value)}catch(e){i.e(e)}finally{i.f()}}}}]),e}(),Fi=".".concat("bs.modal"),Hi="hide".concat(Fi),Bi="hidePrevented".concat(Fi),Wi="hidden".concat(Fi),Ri="show".concat(Fi),zi="shown".concat(Fi),qi="resize".concat(Fi),Vi="click.dismiss".concat(Fi),Ki="mousedown.dismiss".concat(Fi),Qi="keydown.dismiss".concat(Fi),Ui="click".concat(Fi).concat(".data-api"),Xi="modal-open",Yi="show",$i="modal-static",Gi={backdrop:!0,focus:!0,keyboard:!0},Ji={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"},Zi=function(e){function t(e,n){var i;return Ke(this,t),(i=Ne(this,t,[e,n]))._dialog=Kt.findOne(".modal-dialog",i._element),i._backdrop=i._initializeBackDrop(),i._focustrap=i._initializeFocusTrap(),i._isShown=!1,i._isTransitioning=!1,i._scrollBar=new Mi,i._addEventListeners(),i}return We(t,e),Ue(t,[{key:"toggle",value:function(e){return this._isShown?this.hide():this.show(e)}},{key:"show",value:function(e){var t=this;this._isShown||this._isTransitioning||It.trigger(this._element,Ri,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Xi),this._adjustDialog(),this._backdrop.show((function(){return t._showElement(e)})))}},{key:"hide",value:function(){var e=this;this._isShown&&!this._isTransitioning&&(It.trigger(this._element,Hi).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Yi),this._queueCallback((function(){return e._hideModal()}),this._element,this._isAnimated())))}},{key:"dispose",value:function(){It.off(window,Fi),It.off(this._dialog,Fi),this._backdrop.dispose(),this._focustrap.deactivate(),Ie(He(t.prototype),"dispose",this).call(this)}},{key:"handleUpdate",value:function(){this._adjustDialog()}},{key:"_initializeBackDrop",value:function(){return new Ai({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}},{key:"_initializeFocusTrap",value:function(){return new ji({trapElement:this._element})}},{key:"_showElement",value:function(e){var t=this;document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;var n=Kt.findOne(".modal-body",this._dialog);n&&(n.scrollTop=0),ft(this._element),this._element.classList.add(Yi),this._queueCallback((function(){t._config.focus&&t._focustrap.activate(),t._isTransitioning=!1,It.trigger(t._element,zi,{relatedTarget:e})}),this._dialog,this._isAnimated())}},{key:"_addEventListeners",value:function(){var e=this;It.on(this._element,Qi,(function(t){"Escape"===t.key&&(e._config.keyboard?e.hide():e._triggerBackdropTransition())})),It.on(window,qi,(function(){e._isShown&&!e._isTransitioning&&e._adjustDialog()})),It.on(this._element,Ki,(function(t){It.one(e._element,Vi,(function(n){e._element===t.target&&e._element===n.target&&("static"!==e._config.backdrop?e._config.backdrop&&e.hide():e._triggerBackdropTransition())}))}))}},{key:"_hideModal",value:function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((function(){document.body.classList.remove(Xi),e._resetAdjustments(),e._scrollBar.reset(),It.trigger(e._element,Wi)}))}},{key:"_isAnimated",value:function(){return this._element.classList.contains("fade")}},{key:"_triggerBackdropTransition",value:function(){var e=this;if(!It.trigger(this._element,Bi).defaultPrevented){var t=this._element.scrollHeight>document.documentElement.clientHeight,n=this._element.style.overflowY;"hidden"===n||this._element.classList.contains($i)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add($i),this._queueCallback((function(){e._element.classList.remove($i),e._queueCallback((function(){e._element.style.overflowY=n}),e._dialog)}),this._dialog),this._element.focus())}}},{key:"_adjustDialog",value:function(){var e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){var i=pt()?"paddingLeft":"paddingRight";this._element.style[i]="".concat(t,"px")}if(!n&&e){var r=pt()?"paddingRight":"paddingLeft";this._element.style[r]="".concat(t,"px")}}},{key:"_resetAdjustments",value:function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}}],[{key:"Default",get:function(){return Gi}},{key:"DefaultType",get:function(){return Ji}},{key:"NAME",get:function(){return"modal"}},{key:"jQueryInterface",value:function(e,n){return this.each((function(){var i=t.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'.concat(e,'"'));i[e](n)}}))}}]),t}(qt);It.on(document,Ui,'[data-bs-toggle="modal"]',(function(e){var t=this,n=Kt.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),It.one(n,Ri,(function(e){e.defaultPrevented||It.one(n,Wi,(function(){st(t)&&t.focus()}))}));var i=Kt.findOne(".modal.show");i&&Zi.getInstance(i).hide(),Zi.getOrCreateInstance(n).toggle(this)})),Qt(Zi),vt(Zi);var er=".".concat("bs.offcanvas"),tr=".data-api",nr="load".concat(er).concat(tr),ir="show",rr="showing",or="hiding",ar=".offcanvas.show",sr="show".concat(er),cr="shown".concat(er),lr="hide".concat(er),ur="hidePrevented".concat(er),fr="hidden".concat(er),dr="resize".concat(er),hr="click".concat(er).concat(tr),pr="keydown.dismiss".concat(er),vr={backdrop:!0,keyboard:!0,scroll:!1},mr={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"},gr=function(e){function t(e,n){var i;return Ke(this,t),(i=Ne(this,t,[e,n]))._isShown=!1,i._backdrop=i._initializeBackDrop(),i._focustrap=i._initializeFocusTrap(),i._addEventListeners(),i}return We(t,e),Ue(t,[{key:"toggle",value:function(e){return this._isShown?this.hide():this.show(e)}},{key:"show",value:function(e){var t=this;this._isShown||It.trigger(this._element,sr,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new Mi).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(rr),this._queueCallback((function(){t._config.scroll&&!t._config.backdrop||t._focustrap.activate(),t._element.classList.add(ir),t._element.classList.remove(rr),It.trigger(t._element,cr,{relatedTarget:e})}),this._element,!0))}},{key:"hide",value:function(){var e=this;this._isShown&&!It.trigger(this._element,lr).defaultPrevented&&(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(or),this._backdrop.hide(),this._queueCallback((function(){e._element.classList.remove(ir,or),e._element.removeAttribute("aria-modal"),e._element.removeAttribute("role"),e._config.scroll||(new Mi).reset(),It.trigger(e._element,fr)}),this._element,!0))}},{key:"dispose",value:function(){this._backdrop.dispose(),this._focustrap.deactivate(),Ie(He(t.prototype),"dispose",this).call(this)}},{key:"_initializeBackDrop",value:function(){var e=this,t=Boolean(this._config.backdrop);return new Ai({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?function(){"static"!==e._config.backdrop?e.hide():It.trigger(e._element,ur)}:null})}},{key:"_initializeFocusTrap",value:function(){return new ji({trapElement:this._element})}},{key:"_addEventListeners",value:function(){var e=this;It.on(this._element,pr,(function(t){"Escape"===t.key&&(e._config.keyboard?e.hide():It.trigger(e._element,ur))}))}}],[{key:"Default",get:function(){return vr}},{key:"DefaultType",get:function(){return mr}},{key:"NAME",get:function(){return"offcanvas"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e]||e.startsWith("_")||"constructor"===e)throw new TypeError('No method named "'.concat(e,'"'));n[e](this)}}))}}]),t}(qt);It.on(document,hr,'[data-bs-toggle="offcanvas"]',(function(e){var t=this,n=Kt.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),!ct(this)){It.one(n,fr,(function(){st(t)&&t.focus()}));var i=Kt.findOne(ar);i&&i!==n&&gr.getInstance(i).hide(),gr.getOrCreateInstance(n).toggle(this)}})),It.on(window,nr,(function(){var e,t=$e(Kt.find(ar));try{for(t.s();!(e=t.n()).done;){var n=e.value;gr.getOrCreateInstance(n).show()}}catch(e){t.e(e)}finally{t.f()}})),It.on(window,dr,(function(){var e,t=$e(Kt.find("[aria-modal][class*=show][class*=offcanvas-]"));try{for(t.s();!(e=t.n()).done;){var n=e.value;"fixed"!==getComputedStyle(n).position&&gr.getOrCreateInstance(n).hide()}}catch(e){t.e(e)}finally{t.f()}})),Qt(gr),vt(gr);var _r={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},yr=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),br=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,wr=function(e,t){var n=e.nodeName.toLowerCase();return t.includes(n)?!yr.has(n)||Boolean(br.test(e.nodeValue)):t.filter((function(e){return e instanceof RegExp})).some((function(e){return e.test(n)}))},kr={allowList:_r,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Er={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Ar={entry:"(string|element|function|null)",selector:"(string|element)"},Or=function(e){function t(e){var n;return Ke(this,t),(n=Ne(this,t))._config=n._getConfig(e),n}return We(t,e),Ue(t,[{key:"getContent",value:function(){var e=this;return Object.values(this._config.content).map((function(t){return e._resolvePossibleFunction(t)})).filter(Boolean)}},{key:"hasContent",value:function(){return this.getContent().length>0}},{key:"changeContent",value:function(e){return this._checkContent(e),this._config.content=qe(qe({},this._config.content),e),this}},{key:"toHtml",value:function(){var e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(var t=0,n=Object.entries(this._config.content);t<n.length;t++){var i=Ye(n[t],2),r=i[0],o=i[1];this._setContent(e,o,r)}var a,s=e.children[0],c=this._resolvePossibleFunction(this._config.extraClass);return c&&(a=s.classList).add.apply(a,Ge(c.split(" "))),s}},{key:"_typeCheckConfig",value:function(e){Ie(He(t.prototype),"_typeCheckConfig",this).call(this,e),this._checkContent(e.content)}},{key:"_checkContent",value:function(e){for(var n=0,i=Object.entries(e);n<i.length;n++){var r=Ye(i[n],2),o=r[0],a=r[1];Ie(He(t.prototype),"_typeCheckConfig",this).call(this,{selector:o,entry:a},Ar)}}},{key:"_setContent",value:function(e,t,n){var i=Kt.findOne(n,e);i&&((t=this._resolvePossibleFunction(t))?ot(t)?this._putElementInTemplate(at(t),i):this._config.html?i.innerHTML=this._maybeSanitize(t):i.textContent=t:i.remove())}},{key:"_maybeSanitize",value:function(e){return this._config.sanitize?function(e,t,n){var i;if(!e.length)return e;if(n&&"function"==typeof n)return n(e);var r,o=(new window.DOMParser).parseFromString(e,"text/html"),a=$e((i=[]).concat.apply(i,Ge(o.body.querySelectorAll("*"))));try{for(a.s();!(r=a.n()).done;){var s,c=r.value,l=c.nodeName.toLowerCase();if(Object.keys(t).includes(l)){var u,f=(s=[]).concat.apply(s,Ge(c.attributes)),d=[].concat(t["*"]||[],t[l]||[]),h=$e(f);try{for(h.s();!(u=h.n()).done;){var p=u.value;wr(p,d)||c.removeAttribute(p.nodeName)}}catch(e){h.e(e)}finally{h.f()}}else c.remove()}}catch(e){a.e(e)}finally{a.f()}return o.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}},{key:"_resolvePossibleFunction",value:function(e){return mt(e,[this])}},{key:"_putElementInTemplate",value:function(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}],[{key:"Default",get:function(){return kr}},{key:"DefaultType",get:function(){return Er}},{key:"NAME",get:function(){return"TemplateFactory"}}]),t}(zt),Tr=new Set(["sanitize","allowList","sanitizeFn"]),Cr="fade",xr="show",Sr=".".concat("modal"),Lr="hide.bs.modal",jr="hover",Pr="focus",Dr={AUTO:"auto",TOP:"top",RIGHT:pt()?"left":"right",BOTTOM:"bottom",LEFT:pt()?"right":"left"},Ir={allowList:_r,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Nr={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"},Mr=function(e){function t(e,i){var r;if(Ke(this,t),void 0===n)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");return(r=Ne(this,t,[e,i]))._isEnabled=!0,r._timeout=0,r._isHovered=null,r._activeTrigger={},r._popper=null,r._templateFactory=null,r._newContent=null,r.tip=null,r._setListeners(),r._config.selector||r._fixTitle(),r}return We(t,e),Ue(t,[{key:"enable",value:function(){this._isEnabled=!0}},{key:"disable",value:function(){this._isEnabled=!1}},{key:"toggleEnabled",value:function(){this._isEnabled=!this._isEnabled}},{key:"toggle",value:function(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}},{key:"dispose",value:function(){clearTimeout(this._timeout),It.off(this._element.closest(Sr),Lr,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),Ie(He(t.prototype),"dispose",this).call(this)}},{key:"show",value:function(){var e=this;if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(this._isWithContent()&&this._isEnabled){var t=It.trigger(this._element,this.constructor.eventName("show")),n=(lt(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(!t.defaultPrevented&&n){this._disposePopper();var i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));var r=this._config.container;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(i),It.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(xr),"ontouchstart"in document.documentElement){var o,a,s=$e((o=[]).concat.apply(o,Ge(document.body.children)));try{for(s.s();!(a=s.n()).done;){var c=a.value;It.on(c,"mouseover",ut)}}catch(e){s.e(e)}finally{s.f()}}this._queueCallback((function(){It.trigger(e._element,e.constructor.eventName("shown")),!1===e._isHovered&&e._leave(),e._isHovered=!1}),this.tip,this._isAnimated())}}}},{key:"hide",value:function(){var e=this;if(this._isShown()&&!It.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(xr),"ontouchstart"in document.documentElement){var t,n,i=$e((t=[]).concat.apply(t,Ge(document.body.children)));try{for(i.s();!(n=i.n()).done;){var r=n.value;It.off(r,"mouseover",ut)}}catch(e){i.e(e)}finally{i.f()}}this._activeTrigger.click=!1,this._activeTrigger[Pr]=!1,this._activeTrigger[jr]=!1,this._isHovered=null,this._queueCallback((function(){e._isWithActiveTrigger()||(e._isHovered||e._disposePopper(),e._element.removeAttribute("aria-describedby"),It.trigger(e._element,e.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}},{key:"update",value:function(){this._popper&&this._popper.update()}},{key:"_isWithContent",value:function(){return Boolean(this._getTitle())}},{key:"_getTipElement",value:function(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}},{key:"_createTipElement",value:function(e){var t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(Cr,xr),t.classList.add("bs-".concat(this.constructor.NAME,"-auto"));var n=function(e){do{e+=Math.floor(1e6*Math.random())}while(document.getElementById(e));return e}(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(Cr),t}},{key:"setContent",value:function(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}},{key:"_getTemplateFactory",value:function(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new Or(qe(qe({},this._config),{},{content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)})),this._templateFactory}},{key:"_getContentForTemplate",value:function(){return Ve({},".tooltip-inner",this._getTitle())}},{key:"_getTitle",value:function(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}},{key:"_initializeOnDelegatedTarget",value:function(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}},{key:"_isAnimated",value:function(){return this._config.animation||this.tip&&this.tip.classList.contains(Cr)}},{key:"_isShown",value:function(){return this.tip&&this.tip.classList.contains(xr)}},{key:"_createPopper",value:function(e){var t=mt(this._config.placement,[this,e,this._element]),n=Dr[t.toUpperCase()];return Pe(this._element,e,this._getPopperConfig(n))}},{key:"_getOffset",value:function(){var e=this,t=this._config.offset;return"string"==typeof t?t.split(",").map((function(e){return Number.parseInt(e,10)})):"function"==typeof t?function(n){return t(n,e._element)}:t}},{key:"_resolvePossibleFunction",value:function(e){return mt(e,[this._element])}},{key:"_getPopperConfig",value:function(e){var t=this,n={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:".".concat(this.constructor.NAME,"-arrow")}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:function(e){t._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return qe(qe({},n),mt(this._config.popperConfig,[n]))}},{key:"_setListeners",value:function(){var e,t=this,n=$e(this._config.trigger.split(" "));try{for(n.s();!(e=n.n()).done;){var i=e.value;if("click"===i)It.on(this._element,this.constructor.eventName("click"),this._config.selector,(function(e){t._initializeOnDelegatedTarget(e).toggle()}));else if("manual"!==i){var r=i===jr?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),o=i===jr?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");It.on(this._element,r,this._config.selector,(function(e){var n=t._initializeOnDelegatedTarget(e);n._activeTrigger["focusin"===e.type?Pr:jr]=!0,n._enter()})),It.on(this._element,o,this._config.selector,(function(e){var n=t._initializeOnDelegatedTarget(e);n._activeTrigger["focusout"===e.type?Pr:jr]=n._element.contains(e.relatedTarget),n._leave()}))}}}catch(e){n.e(e)}finally{n.f()}this._hideModalHandler=function(){t._element&&t.hide()},It.on(this._element.closest(Sr),Lr,this._hideModalHandler)}},{key:"_fixTitle",value:function(){var e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}},{key:"_enter",value:function(){var e=this;this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((function(){e._isHovered&&e.show()}),this._config.delay.show))}},{key:"_leave",value:function(){var e=this;this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((function(){e._isHovered||e.hide()}),this._config.delay.hide))}},{key:"_setTimeout",value:function(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}},{key:"_isWithActiveTrigger",value:function(){return Object.values(this._activeTrigger).includes(!0)}},{key:"_getConfig",value:function(e){for(var t=Wt(this._element),n=0,i=Object.keys(t);n<i.length;n++){var r=i[n];Tr.has(r)&&delete t[r]}return e=qe(qe({},t),"object"===et(e)&&e?e:{}),e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}},{key:"_configAfterMerge",value:function(e){return e.container=!1===e.container?document.body:at(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}},{key:"_getDelegateConfig",value:function(){for(var e={},t=0,n=Object.entries(this._config);t<n.length;t++){var i=Ye(n[t],2),r=i[0],o=i[1];this.constructor.Default[r]!==o&&(e[r]=o)}return e.selector=!1,e.trigger="manual",e}},{key:"_disposePopper",value:function(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}}],[{key:"Default",get:function(){return Ir}},{key:"DefaultType",get:function(){return Nr}},{key:"NAME",get:function(){return"tooltip"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'.concat(e,'"'));n[e]()}}))}}]),t}(qt);vt(Mr);var Fr=qe(qe({},Mr.Default),{},{content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"}),Hr=qe(qe({},Mr.DefaultType),{},{content:"(null|string|element|function)"}),Br=function(e){function t(){return Ke(this,t),Ne(this,t,arguments)}return We(t,e),Ue(t,[{key:"_isWithContent",value:function(){return this._getTitle()||this._getContent()}},{key:"_getContentForTemplate",value:function(){return Ve(Ve({},".popover-header",this._getTitle()),".popover-body",this._getContent())}},{key:"_getContent",value:function(){return this._resolvePossibleFunction(this._config.content)}}],[{key:"Default",get:function(){return Fr}},{key:"DefaultType",get:function(){return Hr}},{key:"NAME",get:function(){return"popover"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'.concat(e,'"'));n[e]()}}))}}]),t}(Mr);vt(Br);var Wr=".".concat("bs.scrollspy"),Rr="activate".concat(Wr),zr="click".concat(Wr),qr="load".concat(Wr).concat(".data-api"),Vr="active",Kr="[href]",Qr=".nav-link",Ur="".concat(Qr,", ").concat(".nav-item"," > ").concat(Qr,", ").concat(".list-group-item"),Xr={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Yr={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"},$r=function(e){function t(e,n){var i;return Ke(this,t),(i=Ne(this,t,[e,n]))._targetLinks=new Map,i._observableSections=new Map,i._rootElement="visible"===getComputedStyle(i._element).overflowY?null:i._element,i._activeTarget=null,i._observer=null,i._previousScrollData={visibleEntryTop:0,parentScrollTop:0},i.refresh(),i}return We(t,e),Ue(t,[{key:"refresh",value:function(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();var e,t=$e(this._observableSections.values());try{for(t.s();!(e=t.n()).done;){var n=e.value;this._observer.observe(n)}}catch(e){t.e(e)}finally{t.f()}}},{key:"dispose",value:function(){this._observer.disconnect(),Ie(He(t.prototype),"dispose",this).call(this)}},{key:"_configAfterMerge",value:function(e){return e.target=at(e.target)||document.body,e.rootMargin=e.offset?"".concat(e.offset,"px 0px -30%"):e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map((function(e){return Number.parseFloat(e)}))),e}},{key:"_maybeEnableSmoothScroll",value:function(){var e=this;this._config.smoothScroll&&(It.off(this._config.target,zr),It.on(this._config.target,zr,Kr,(function(t){var n=e._observableSections.get(t.target.hash);if(n){t.preventDefault();var i=e._rootElement||window,r=n.offsetTop-e._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:r,behavior:"smooth"});i.scrollTop=r}})))}},{key:"_getNewObserver",value:function(){var e=this,t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((function(t){return e._observerCallback(t)}),t)}},{key:"_observerCallback",value:function(e){var t=this,n=function(e){return t._targetLinks.get("#".concat(e.target.id))},i=function(e){t._previousScrollData.visibleEntryTop=e.target.offsetTop,t._process(n(e))},r=(this._rootElement||document.documentElement).scrollTop,o=r>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=r;var a,s=$e(e);try{for(s.s();!(a=s.n()).done;){var c=a.value;if(c.isIntersecting){var l=c.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(o&&l){if(i(c),!r)return}else o||l||i(c)}else this._activeTarget=null,this._clearActiveClass(n(c))}}catch(e){s.e(e)}finally{s.f()}}},{key:"_initializeTargetsAndObservables",value:function(){this._targetLinks=new Map,this._observableSections=new Map;var e,t=$e(Kt.find(Kr,this._config.target));try{for(t.s();!(e=t.n()).done;){var n=e.value;if(n.hash&&!ct(n)){var i=Kt.findOne(decodeURI(n.hash),this._element);st(i)&&(this._targetLinks.set(decodeURI(n.hash),n),this._observableSections.set(n.hash,i))}}}catch(e){t.e(e)}finally{t.f()}}},{key:"_process",value:function(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(Vr),this._activateParents(e),It.trigger(this._element,Rr,{relatedTarget:e}))}},{key:"_activateParents",value:function(e){if(e.classList.contains("dropdown-item"))Kt.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(Vr);else{var t,n=$e(Kt.parents(e,".nav, .list-group"));try{for(n.s();!(t=n.n()).done;){var i,r=t.value,o=$e(Kt.prev(r,Ur));try{for(o.s();!(i=o.n()).done;)i.value.classList.add(Vr)}catch(e){o.e(e)}finally{o.f()}}}catch(e){n.e(e)}finally{n.f()}}}},{key:"_clearActiveClass",value:function(e){e.classList.remove(Vr);var t,n=$e(Kt.find("".concat(Kr,".").concat(Vr),e));try{for(n.s();!(t=n.n()).done;)t.value.classList.remove(Vr)}catch(e){n.e(e)}finally{n.f()}}}],[{key:"Default",get:function(){return Xr}},{key:"DefaultType",get:function(){return Yr}},{key:"NAME",get:function(){return"scrollspy"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e]||e.startsWith("_")||"constructor"===e)throw new TypeError('No method named "'.concat(e,'"'));n[e]()}}))}}]),t}(qt);It.on(window,qr,(function(){var e,t=$e(Kt.find('[data-bs-spy="scroll"]'));try{for(t.s();!(e=t.n()).done;){var n=e.value;$r.getOrCreateInstance(n)}}catch(e){t.e(e)}finally{t.f()}})),vt($r);var Gr=".".concat("bs.tab"),Jr="hide".concat(Gr),Zr="hidden".concat(Gr),eo="show".concat(Gr),to="shown".concat(Gr),no="click".concat(Gr),io="keydown".concat(Gr),ro="load".concat(Gr),oo="ArrowLeft",ao="ArrowRight",so="ArrowUp",co="ArrowDown",lo="Home",uo="End",fo="active",ho="fade",po="show",vo=".dropdown-toggle",mo=":not(".concat(vo,")"),go=".nav-link".concat(mo,", .list-group-item").concat(mo,', [role="tab"]').concat(mo),_o='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',yo="".concat(go,", ").concat(_o),bo=".".concat(fo,'[data-bs-toggle="tab"], .').concat(fo,'[data-bs-toggle="pill"], .').concat(fo,'[data-bs-toggle="list"]'),wo=function(e){function t(e){var n;return Ke(this,t),(n=Ne(this,t,[e]))._parent=n._element.closest('.list-group, .nav, [role="tablist"]'),n._parent?(n._setInitialAttributes(n._parent,n._getChildren()),It.on(n._element,io,(function(e){return n._keydown(e)})),n):Me(n)}return We(t,e),Ue(t,[{key:"show",value:function(){var e=this._element;if(!this._elemIsActive(e)){var t=this._getActiveElem(),n=t?It.trigger(t,Jr,{relatedTarget:e}):null;It.trigger(e,eo,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}}},{key:"_activate",value:function(e,t){var n=this;e&&(e.classList.add(fo),this._activate(Kt.getElementFromSelector(e)),this._queueCallback((function(){"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),n._toggleDropDown(e,!0),It.trigger(e,to,{relatedTarget:t})):e.classList.add(po)}),e,e.classList.contains(ho)))}},{key:"_deactivate",value:function(e,t){var n=this;e&&(e.classList.remove(fo),e.blur(),this._deactivate(Kt.getElementFromSelector(e)),this._queueCallback((function(){"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),n._toggleDropDown(e,!1),It.trigger(e,Zr,{relatedTarget:t})):e.classList.remove(po)}),e,e.classList.contains(ho)))}},{key:"_keydown",value:function(e){if([oo,ao,so,co,lo,uo].includes(e.key)){e.stopPropagation(),e.preventDefault();var n,i=this._getChildren().filter((function(e){return!ct(e)}));if([lo,uo].includes(e.key))n=i[e.key===lo?0:i.length-1];else{var r=[ao,co].includes(e.key);n=_t(i,e.target,r,!0)}n&&(n.focus({preventScroll:!0}),t.getOrCreateInstance(n).show())}}},{key:"_getChildren",value:function(){return Kt.find(yo,this._parent)}},{key:"_getActiveElem",value:function(){var e=this;return this._getChildren().find((function(t){return e._elemIsActive(t)}))||null}},{key:"_setInitialAttributes",value:function(e,t){this._setAttributeIfNotExists(e,"role","tablist");var n,i=$e(t);try{for(i.s();!(n=i.n()).done;){var r=n.value;this._setInitialAttributesOnChild(r)}}catch(e){i.e(e)}finally{i.f()}}},{key:"_setInitialAttributesOnChild",value:function(e){e=this._getInnerElement(e);var t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}},{key:"_setInitialAttributesOnTargetPanel",value:function(e){var t=Kt.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby","".concat(e.id)))}},{key:"_toggleDropDown",value:function(e,t){var n=this._getOuterElement(e);if(n.classList.contains("dropdown")){var i=function(e,i){var r=Kt.findOne(e,n);r&&r.classList.toggle(i,t)};i(vo,fo),i(".dropdown-menu",po),n.setAttribute("aria-expanded",t)}}},{key:"_setAttributeIfNotExists",value:function(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}},{key:"_elemIsActive",value:function(e){return e.classList.contains(fo)}},{key:"_getInnerElement",value:function(e){return e.matches(yo)?e:Kt.findOne(yo,e)}},{key:"_getOuterElement",value:function(e){return e.closest(".nav-item, .list-group-item")||e}}],[{key:"NAME",get:function(){return"tab"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this);if("string"==typeof e){if(void 0===n[e]||e.startsWith("_")||"constructor"===e)throw new TypeError('No method named "'.concat(e,'"'));n[e]()}}))}}]),t}(qt);It.on(document,no,_o,(function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),ct(this)||wo.getOrCreateInstance(this).show()})),It.on(window,ro,(function(){var e,t=$e(Kt.find(bo));try{for(t.s();!(e=t.n()).done;){var n=e.value;wo.getOrCreateInstance(n)}}catch(e){t.e(e)}finally{t.f()}})),vt(wo);var ko=".".concat("bs.toast"),Eo="mouseover".concat(ko),Ao="mouseout".concat(ko),Oo="focusin".concat(ko),To="focusout".concat(ko),Co="hide".concat(ko),xo="hidden".concat(ko),So="show".concat(ko),Lo="shown".concat(ko),jo="hide",Po="show",Do="showing",Io={animation:"boolean",autohide:"boolean",delay:"number"},No={animation:!0,autohide:!0,delay:5e3},Mo=function(e){function t(e,n){var i;return Ke(this,t),(i=Ne(this,t,[e,n]))._timeout=null,i._hasMouseInteraction=!1,i._hasKeyboardInteraction=!1,i._setListeners(),i}return We(t,e),Ue(t,[{key:"show",value:function(){var e=this;It.trigger(this._element,So).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(jo),ft(this._element),this._element.classList.add(Po,Do),this._queueCallback((function(){e._element.classList.remove(Do),It.trigger(e._element,Lo),e._maybeScheduleHide()}),this._element,this._config.animation))}},{key:"hide",value:function(){var e=this;this.isShown()&&!It.trigger(this._element,Co).defaultPrevented&&(this._element.classList.add(Do),this._queueCallback((function(){e._element.classList.add(jo),e._element.classList.remove(Do,Po),It.trigger(e._element,xo)}),this._element,this._config.animation))}},{key:"dispose",value:function(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Po),Ie(He(t.prototype),"dispose",this).call(this)}},{key:"isShown",value:function(){return this._element.classList.contains(Po)}},{key:"_maybeScheduleHide",value:function(){var e=this;this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((function(){e.hide()}),this._config.delay)))}},{key:"_onInteraction",value:function(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)this._clearTimeout();else{var n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}}},{key:"_setListeners",value:function(){var e=this;It.on(this._element,Eo,(function(t){return e._onInteraction(t,!0)})),It.on(this._element,Ao,(function(t){return e._onInteraction(t,!1)})),It.on(this._element,Oo,(function(t){return e._onInteraction(t,!0)})),It.on(this._element,To,(function(t){return e._onInteraction(t,!1)}))}},{key:"_clearTimeout",value:function(){clearTimeout(this._timeout),this._timeout=null}}],[{key:"Default",get:function(){return No}},{key:"DefaultType",get:function(){return Io}},{key:"NAME",get:function(){return"toast"}},{key:"jQueryInterface",value:function(e){return this.each((function(){var n=t.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'.concat(e,'"'));n[e](this)}}))}}]),t}(qt);Qt(Mo),vt(Mo);try{window.bootstrap=i}catch(e){}return t}()}));