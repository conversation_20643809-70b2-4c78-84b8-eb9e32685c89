# Test Configuration

# Database Configuration for Tests
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for Tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Disable Liquibase for tests
spring.liquibase.enabled=false

# Logging Configuration
logging.level.org.springframework.web=DEBUG
logging.level.ag.fuel.jobify=DEBUG

# Security Configuration for Tests
app.jwt.secret=test-secret-key-for-testing-purposes-only-not-for-production-use
app.jwt.expiration=3600000

# Email Configuration for Tests (disabled)
spring.mail.host=localhost
spring.mail.port=25
spring.mail.username=test
spring.mail.password=test
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# Cache Configuration for Tests
spring.cache.type=simple

# File Upload Configuration for Tests
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=1MB

# Test specific properties
test.database.cleanup=true
test.notifications.enabled=false
