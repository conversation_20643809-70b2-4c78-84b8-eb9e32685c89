package ag.fuel.jobify.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for bulk testing parameters
 * Allows easy adjustment of test parameters without modifying code
 */
@Configuration
@ConfigurationProperties(prefix = "test.bulk")
public class BulkTestConfig {
    
    /**
     * Number of notifications to create in bulk insert tests
     * Default: 1000
     * Can be overridden in application-test.properties with: test.bulk.notification-count=500
     */
    private int notificationCount = 1000;
    
    /**
     * Batch size for processing notifications
     * Default: 100
     */
    private int batchSize = 100;
    
    /**
     * Whether to log progress during bulk operations
     * Default: true
     */
    private boolean logProgress = true;
    
    /**
     * Whether to create test users if none exist
     * Default: true
     */
    private boolean createTestUsers = true;
    
    /**
     * Number of test users to create if needed
     * Default: 5
     */
    private int testUserCount = 5;

    /**
     * Whether the bulk insert should be transactional (rollback after test) or definitive (permanent)
     * Default: true (transactional - safe for testing)
     * Set to false for permanent records
     */
    private boolean transactional = true;

    /**
     * Email of the real user to assign notifications to when creating definitive records
     * Only used when transactional = false
     * If null or empty, will use SYSTEM notifications for all users
     */
    private String realUserEmail;

    /**
     * Whether to create SYSTEM notifications (for all users) or USER notifications (for specific user)
     * Default: true (SYSTEM notifications)
     * Set to false to create USER notifications for the specified realUserEmail
     */
    private boolean systemNotifications = true;

    // Getters and Setters
    
    public int getNotificationCount() {
        return notificationCount;
    }

    public void setNotificationCount(int notificationCount) {
        this.notificationCount = notificationCount;
    }

    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }

    public boolean isLogProgress() {
        return logProgress;
    }

    public void setLogProgress(boolean logProgress) {
        this.logProgress = logProgress;
    }

    public boolean isCreateTestUsers() {
        return createTestUsers;
    }

    public void setCreateTestUsers(boolean createTestUsers) {
        this.createTestUsers = createTestUsers;
    }

    public int getTestUserCount() {
        return testUserCount;
    }

    public void setTestUserCount(int testUserCount) {
        this.testUserCount = testUserCount;
    }

    public boolean isTransactional() {
        return transactional;
    }

    public void setTransactional(boolean transactional) {
        this.transactional = transactional;
    }

    public String getRealUserEmail() {
        return realUserEmail;
    }

    public void setRealUserEmail(String realUserEmail) {
        this.realUserEmail = realUserEmail;
    }

    public boolean isSystemNotifications() {
        return systemNotifications;
    }

    public void setSystemNotifications(boolean systemNotifications) {
        this.systemNotifications = systemNotifications;
    }
}
