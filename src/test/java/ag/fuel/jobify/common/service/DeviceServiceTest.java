package ag.fuel.jobify.common.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;

class DeviceServiceTest {

    private static final String WINDOWS_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    private static final String MAC_USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    private static final String LINUX_USER_AGENT = "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    private static final String ANDROID_USER_AGENT = "Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36";
    private static final String IPAD_USER_AGENT = "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1";
    private static final String MOBILE_USER_AGENT = "Mozilla/5.0 (Mobile; rv:26.0) Gecko/26.0 Firefox/26.0";
    private static final String TABLET_USER_AGENT = "Mozilla/5.0 (Tablet; rv:26.0) Gecko/26.0 Firefox/26.0";

    @InjectMocks
    private DeviceService deviceService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetDeviceName_Windows() {
        // Act
        String device = deviceService.getDeviceName(WINDOWS_USER_AGENT);

        // Assert
        assertEquals("Windows PC", device);
    }

    @Test
    void testGetDeviceName_Mac() {
        // Act
        String device = deviceService.getDeviceName(MAC_USER_AGENT);

        // Assert
        assertEquals("Mac", device);
    }

    @Test
    void testGetDeviceName_Linux() {
        // Act
        String device = deviceService.getDeviceName(LINUX_USER_AGENT);

        // Assert
        assertEquals("Linux PC", device);
    }

    @Test
    void testGetDeviceName_Android() {
        // Act
        String device = deviceService.getDeviceName(ANDROID_USER_AGENT);

        // Assert
        assertEquals("Android Mobile", device);
    }

    @Test
    void testGetDeviceName_iPad() {
        // Act
        String device = deviceService.getDeviceName(IPAD_USER_AGENT);

        // Assert
        assertEquals("iPad", device);
    }

    @Test
    void testGetDeviceName_Mobile() {
        // Act
        String device = deviceService.getDeviceName(MOBILE_USER_AGENT);

        // Assert
        assertEquals("Mobile Device", device);
    }

    @Test
    void testGetDeviceName_Tablet() {
        // Act
        String device = deviceService.getDeviceName(TABLET_USER_AGENT);

        // Assert
        assertEquals("Tablet", device);
    }

    @Test
    void testGetDeviceName_UnknownUserAgent() {
        // Act
        String device = deviceService.getDeviceName("Unknown User Agent");

        // Assert
        assertEquals("Desktop", device);
    }

    @Test
    void testGetDeviceName_NullUserAgent() {
        // Act
        String device = deviceService.getDeviceName(null);

        // Assert
        assertEquals("Unknown Device", device);
    }

    @Test
    void testGetDeviceName_EmptyUserAgent() {
        // Act
        String device = deviceService.getDeviceName("");

        // Assert
        assertEquals("Unknown Device", device);
    }
}
