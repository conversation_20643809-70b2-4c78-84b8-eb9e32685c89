package ag.fuel.jobify.common.service;

import ag.fuel.jobify.common.service.BrowserService;

import eu.bitwalker.useragentutils.UserAgent;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

class BrowserServiceTest {

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private BrowserService browserService;

    private static final String CHROME_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
    private static final String FIREFOX_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0";
    private static final String EDGE_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59";
    private static final String OPERA_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.277";
    private static final String SAFARI_USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15";
    private static final String SAMSUNG_BROWSER_USER_AGENT = "Mozilla/5.0 (Linux; Android 10; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/13.0 Chrome/83.0.4103.106 Mobile Safari/537.36";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getBrowserFromRequest_WithChromeUserAgent_ShouldReturnChrome() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(CHROME_USER_AGENT);

        // Act
        String browser = browserService.getBrowserFromRequest(request);

        // Assert
        assertEquals("Chrome", browser);
    }

    @Test
    void getBrowserFromRequest_WithFirefoxUserAgent_ShouldReturnFirefox() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(FIREFOX_USER_AGENT);

        // Act
        String browser = browserService.getBrowserFromRequest(request);

        // Assert
        assertEquals("Firefox", browser);
    }

    @Test
    void getBrowserFromRequest_WithEdgeUserAgent_ShouldReturnEdge() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(EDGE_USER_AGENT);

        // Act
        String browser = browserService.getBrowserFromRequest(request);

        // Assert
        assertEquals("Edge", browser);
    }

    @Test
    void getBrowserFromRequest_WithOperaUserAgent_ShouldReturnOpera() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(OPERA_USER_AGENT);

        // Act
        String browser = browserService.getBrowserFromRequest(request);

        // Assert
        assertEquals("Opera", browser);
    }

    @Test
    void getBrowserFromRequest_WithSafariUserAgent_ShouldReturnSafari() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(SAFARI_USER_AGENT);

        // Act
        String browser = browserService.getBrowserFromRequest(request);

        // Assert
        assertEquals("Safari", browser);
    }

    @Test
    void getBrowserFromRequest_WithNullUserAgent_ShouldReturnUnknown() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(null);

        // Act
        String browser = browserService.getBrowserFromRequest(request);

        // Assert
        assertEquals("Unknown", browser);
    }

    @Test
    void getBrowserFromRequest_WithSamsungBrowserUserAgent_ShouldReturnSamsungInternetBrowser() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(SAMSUNG_BROWSER_USER_AGENT);

        // Act
        String browser = browserService.getBrowserFromRequest(request);

        // Assert
        assertEquals("Samsung Internet Browser", browser);
    }

    @Test
    void getOperatingSystemFromRequest_WithWindowsUserAgent_ShouldReturnWindows() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(CHROME_USER_AGENT);

        // Act
        String os = browserService.getOperatingSystemFromRequest(request);

        // Assert
        assertEquals("Windows", os);
    }

    @Test
    void getDeviceTypeFromRequest_WithDesktopUserAgent_ShouldReturnComputer() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(CHROME_USER_AGENT);

        // Act
        String deviceType = browserService.getDeviceTypeFromRequest(request);

        // Assert
        assertEquals("Computer", deviceType);
    }

    @Test
    void getUserAgentFromRequest_ShouldReturnUserAgentObject() {
        // Arrange
        when(request.getHeader("User-Agent")).thenReturn(CHROME_USER_AGENT);

        // Act
        UserAgent userAgent = browserService.getUserAgentFromRequest(request);

        // Assert
        assertNotNull(userAgent);
        // Just check that it contains "Chrome" since the exact version format may vary
        assertTrue(userAgent.getBrowser().getName().contains("Chrome"));
    }

    @Test
    void getBrowserFromUserAgentString_WithChromeUserAgent_ShouldReturnChrome() {
        // Act
        String browser = browserService.getBrowserFromUserAgentString(CHROME_USER_AGENT);

        // Assert
        assertEquals("Chrome", browser);
    }

    @Test
    void getBrowserFromUserAgentString_WithFirefoxUserAgent_ShouldReturnFirefox() {
        // Act
        String browser = browserService.getBrowserFromUserAgentString(FIREFOX_USER_AGENT);

        // Assert
        assertEquals("Firefox", browser);
    }

    @Test
    void getBrowserFromUserAgentString_WithEdgeUserAgent_ShouldReturnEdge() {
        // Act
        String browser = browserService.getBrowserFromUserAgentString(EDGE_USER_AGENT);

        // Assert
        assertEquals("Edge", browser);
    }

    @Test
    void getBrowserFromUserAgentString_WithOperaUserAgent_ShouldReturnOpera() {
        // Act
        String browser = browserService.getBrowserFromUserAgentString(OPERA_USER_AGENT);

        // Assert
        assertEquals("Opera", browser);
    }

    @Test
    void getBrowserFromUserAgentString_WithSafariUserAgent_ShouldReturnSafari() {
        // Act
        String browser = browserService.getBrowserFromUserAgentString(SAFARI_USER_AGENT);

        // Assert
        assertEquals("Safari", browser);
    }

    @Test
    void getBrowserFromUserAgentString_WithNullUserAgent_ShouldReturnUnknown() {
        // Act
        String browser = browserService.getBrowserFromUserAgentString(null);

        // Assert
        assertEquals("Unknown", browser);
    }

    @Test
    void getBrowserFromUserAgentString_WithSamsungBrowserUserAgent_ShouldReturnSamsungInternetBrowser() {
        // Act
        String browser = browserService.getBrowserFromUserAgentString(SAMSUNG_BROWSER_USER_AGENT);

        // Assert
        assertEquals("Samsung Internet Browser", browser);
    }
}
