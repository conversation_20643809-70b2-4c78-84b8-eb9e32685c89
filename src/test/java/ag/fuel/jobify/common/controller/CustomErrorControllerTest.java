package ag.fuel.jobify.common.controller;

import ag.fuel.jobify.common.controller.CustomErrorController;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.CannotGetJdbcConnectionException;

import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * Test class for CustomErrorController.
 * Tests the controller's ability to detect and handle database connection errors.
 */
class CustomErrorControllerTest {

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private CustomErrorController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void handleError_WithSQLException_ShouldReturnDatabaseConnectionErrorPage() {
        // Arrange
        SQLException sqlException = new SQLException("Database error");
        when(request.getAttribute(RequestDispatcher.ERROR_EXCEPTION)).thenReturn(sqlException);

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/database-connection-error", viewName);
    }

    @Test
    void handleError_WithDataAccessResourceFailureException_ShouldReturnDatabaseConnectionErrorPage() {
        // Arrange
        DataAccessResourceFailureException exception = new DataAccessResourceFailureException("Database error");
        when(request.getAttribute(RequestDispatcher.ERROR_EXCEPTION)).thenReturn(exception);

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/database-connection-error", viewName);
    }

    @Test
    void handleError_WithCannotGetJdbcConnectionException_ShouldReturnDatabaseConnectionErrorPage() {
        // Arrange
        CannotGetJdbcConnectionException exception = new CannotGetJdbcConnectionException("Cannot get connection");
        when(request.getAttribute(RequestDispatcher.ERROR_EXCEPTION)).thenReturn(exception);

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/database-connection-error", viewName);
    }

    @Test
    void handleError_WithConnectionClosedMessage_ShouldReturnDatabaseConnectionErrorPage() {
        // Arrange
        RuntimeException exception = new RuntimeException("The connection is closed");
        when(request.getAttribute(RequestDispatcher.ERROR_EXCEPTION)).thenReturn(exception);

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/database-connection-error", viewName);
    }

    @Test
    void handleError_WithConnectionRefusedMessage_ShouldReturnDatabaseConnectionErrorPage() {
        // Arrange
        RuntimeException exception = new RuntimeException("Connection refused");
        when(request.getAttribute(RequestDispatcher.ERROR_EXCEPTION)).thenReturn(exception);

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/database-connection-error", viewName);
    }

    @Test
    void handleError_WithConnectionTimeoutMessage_ShouldReturnDatabaseConnectionErrorPage() {
        // Arrange
        RuntimeException exception = new RuntimeException("Connection timed out");
        when(request.getAttribute(RequestDispatcher.ERROR_EXCEPTION)).thenReturn(exception);

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/database-connection-error", viewName);
    }

    @Test
    void handleError_WithNestedSQLException_ShouldReturnDatabaseConnectionErrorPage() {
        // Arrange
        SQLException sqlException = new SQLException("Database error");
        RuntimeException exception = new RuntimeException("Wrapper exception", sqlException);
        when(request.getAttribute(RequestDispatcher.ERROR_EXCEPTION)).thenReturn(exception);

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/database-connection-error", viewName);
    }

    @Test
    void handleError_WithNotFoundStatus_ShouldReturn404Page() {
        // Arrange
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(HttpStatus.NOT_FOUND.value());

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/error-404", viewName);
    }

    @Test
    void handleError_WithInternalServerErrorStatus_ShouldReturn500Page() {
        // Arrange
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(HttpStatus.INTERNAL_SERVER_ERROR.value());

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/error-500", viewName);
    }

    @Test
    void handleError_WithForbiddenStatus_ShouldReturn403Page() {
        // Arrange
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(HttpStatus.FORBIDDEN.value());

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/error-403", viewName);
    }

    @Test
    void handleError_WithNoStatusOrException_ShouldReturnGenericErrorPage() {
        // Arrange - no setup needed

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/generic-error", viewName);
    }

    @Test
    void handleError_WithFaviconRequest_ShouldReturn404PageWithDebugLogging() {
        // Arrange
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(HttpStatus.NOT_FOUND.value());
        when(request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI)).thenReturn("/favicon.ico");

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/error-404", viewName);
    }

    @Test
    void handleError_WithChromeDevToolsRequest_ShouldReturn404PageWithDebugLogging() {
        // Arrange
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(HttpStatus.NOT_FOUND.value());
        when(request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI)).thenReturn("/.well-known/appspecific/com.chrome.devtools.json");

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/error-404", viewName);
    }

    @Test
    void handleError_WithAppleTouchIconRequest_ShouldReturn404PageWithDebugLogging() {
        // Arrange
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(HttpStatus.NOT_FOUND.value());
        when(request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI)).thenReturn("/apple-touch-icon-120x120.png");

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/error-404", viewName);
    }

    @Test
    void handleError_WithRobotsRequest_ShouldReturn404PageWithDebugLogging() {
        // Arrange
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(HttpStatus.NOT_FOUND.value());
        when(request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI)).thenReturn("/robots.txt");

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/error-404", viewName);
    }

    @Test
    void handleError_WithRegularRequest_ShouldReturn404PageWithInfoLogging() {
        // Arrange
        when(request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE)).thenReturn(HttpStatus.NOT_FOUND.value());
        when(request.getAttribute(RequestDispatcher.ERROR_REQUEST_URI)).thenReturn("/some/regular/path");

        // Act
        String viewName = controller.handleError(request);

        // Assert
        assertEquals("/errors/error-404", viewName);
    }
}