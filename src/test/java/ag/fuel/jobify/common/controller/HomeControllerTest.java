package ag.fuel.jobify.common.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;

class HomeControllerTest {

    @InjectMocks
    private HomeController homeController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void home_ShouldReturnIndexView() {
        // Act
        String result = homeController.home();

        // Assert
        assertEquals("index", result);
    }

    @Test
    void signup_ShouldReturnSignupView() {
        // Act
        String result = homeController.signup();

        // Assert
        assertEquals("signup", result);
    }
}
