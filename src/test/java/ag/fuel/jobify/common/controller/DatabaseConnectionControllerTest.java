package ag.fuel.jobify.common.controller;

import ag.fuel.jobify.common.controller.DatabaseConnectionController;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.sql.DataSource;
import java.net.SocketException;
import java.sql.Connection;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Test class for DatabaseConnectionController.
 * Tests the controller's ability to handle database connection issues.
 */
class DatabaseConnectionControllerTest {

    @Mock
    private DataSource dataSource;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private RedirectAttributes redirectAttributes;

    @Mock
    private Connection connection;

    @InjectMocks
    private DatabaseConnectionController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void reloadConnection_Success_ShouldRedirectToHome() {
        // Arrange
        when(jdbcTemplate.queryForObject("SELECT 1", Integer.class)).thenReturn(1);

        // Act
        String result = controller.reloadConnection(redirectAttributes);

        // Assert
        assertEquals("redirect:/", result);
        verify(redirectAttributes).addFlashAttribute("message", "Database connection successfully reloaded");
    }

    @Test
    void reloadConnection_QueryReturnsNull_ShouldRedirectToErrorPage() {
        // Arrange
        when(jdbcTemplate.queryForObject("SELECT 1", Integer.class)).thenReturn(null);

        // Act
        String result = controller.reloadConnection(redirectAttributes);

        // Assert
        assertEquals("redirect:/errors/database-connection-error", result);
    }

    @Test
    void reloadConnection_QueryReturnsWrongValue_ShouldRedirectToErrorPage() {
        // Arrange
        when(jdbcTemplate.queryForObject("SELECT 1", Integer.class)).thenReturn(2);

        // Act
        String result = controller.reloadConnection(redirectAttributes);

        // Assert
        assertEquals("redirect:/errors/database-connection-error", result);
    }

    @Test
    void reloadConnection_ThrowsConnectionClosed_ShouldPropagateException() {
        // Arrange
        when(jdbcTemplate.queryForObject(anyString(), any(Class.class)))
                .thenThrow(new CannotGetJdbcConnectionException("Connection is closed"));

        // Act & Assert
        Exception exception = assertThrows(CannotGetJdbcConnectionException.class, () -> {
            controller.reloadConnection(redirectAttributes);
        });

        assertTrue(exception.getMessage().contains("Connection is closed"));
    }

    @Test
    void reloadConnection_ThrowsSocketClosed_ShouldPropagateException() {
        // Arrange
        CannotGetJdbcConnectionException exception = new CannotGetJdbcConnectionException("Socket closed");
        when(jdbcTemplate.queryForObject(anyString(), any(Class.class)))
                .thenThrow(exception);

        // Act & Assert
        Exception thrownException = assertThrows(CannotGetJdbcConnectionException.class, () -> {
            controller.reloadConnection(redirectAttributes);
        });

        assertTrue(thrownException.getMessage().contains("Socket closed"));
    }

    @Test
    void reloadConnection_ThrowsConnectionTimeout_ShouldPropagateException() {
        // Arrange
        CannotGetJdbcConnectionException exception = new CannotGetJdbcConnectionException("Connection timed out");
        when(jdbcTemplate.queryForObject(anyString(), any(Class.class)))
                .thenThrow(exception);

        // Act & Assert
        Exception thrownException = assertThrows(CannotGetJdbcConnectionException.class, () -> {
            controller.reloadConnection(redirectAttributes);
        });

        assertTrue(thrownException.getMessage().contains("Connection timed out"));
    }
}
