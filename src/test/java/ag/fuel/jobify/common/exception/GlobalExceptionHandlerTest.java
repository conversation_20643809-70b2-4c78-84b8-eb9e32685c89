package ag.fuel.jobify.common.exception;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.ProblemDetail;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.security.authentication.AccountExpiredException;

import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @Mock
    private MessageSource messageSource;

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @BeforeEach
    void setUp() {
        // Set default locale for testing
        LocaleContextHolder.setLocale(Locale.ENGLISH);
    }

    @Test
    void handleSecurityException_DisabledException_ShouldReturnLocalizedMessage() {
        // Arrange
        DisabledException exception = new DisabledException("User is disabled");
        String expectedMessage = "Your account is disabled. Please contact support to activate your account.";
        
        when(messageSource.getMessage(eq("auth.error.user.disabled"), isNull(), any(Locale.class)))
                .thenReturn(expectedMessage);

        // Act
        ProblemDetail result = globalExceptionHandler.handleSecurityException(exception);

        // Assert
        assertNotNull(result);
        assertEquals(403, result.getStatus());
        assertEquals(expectedMessage, result.getDetail());
        assertEquals(expectedMessage, result.getProperties().get("description"));
        
        verify(messageSource).getMessage(eq("auth.error.user.disabled"), isNull(), any(Locale.class));
    }

    @Test
    void handleSecurityException_LockedException_ShouldReturnLocalizedMessage() {
        // Arrange
        LockedException exception = new LockedException("Account is locked");
        String expectedMessage = "Your account has been locked. Please contact support.";
        
        when(messageSource.getMessage(eq("auth.error.account.locked"), isNull(), any(Locale.class)))
                .thenReturn(expectedMessage);

        // Act
        ProblemDetail result = globalExceptionHandler.handleSecurityException(exception);

        // Assert
        assertNotNull(result);
        assertEquals(403, result.getStatus());
        assertEquals(expectedMessage, result.getDetail());
        assertEquals(expectedMessage, result.getProperties().get("description"));
        
        verify(messageSource).getMessage(eq("auth.error.account.locked"), isNull(), any(Locale.class));
    }

    @Test
    void handleSecurityException_CredentialsExpiredException_ShouldReturnLocalizedMessage() {
        // Arrange
        CredentialsExpiredException exception = new CredentialsExpiredException("Credentials expired");
        String expectedMessage = "Your credentials have expired. Please reset your password.";
        
        when(messageSource.getMessage(eq("auth.error.credentials.expired"), isNull(), any(Locale.class)))
                .thenReturn(expectedMessage);

        // Act
        ProblemDetail result = globalExceptionHandler.handleSecurityException(exception);

        // Assert
        assertNotNull(result);
        assertEquals(403, result.getStatus());
        assertEquals(expectedMessage, result.getDetail());
        assertEquals(expectedMessage, result.getProperties().get("description"));
        
        verify(messageSource).getMessage(eq("auth.error.credentials.expired"), isNull(), any(Locale.class));
    }

    @Test
    void handleSecurityException_AccountExpiredException_ShouldReturnLocalizedMessage() {
        // Arrange
        AccountExpiredException exception = new AccountExpiredException("Account expired");
        String expectedMessage = "Your account has expired. Please contact support.";
        
        when(messageSource.getMessage(eq("auth.error.account.expired"), isNull(), any(Locale.class)))
                .thenReturn(expectedMessage);

        // Act
        ProblemDetail result = globalExceptionHandler.handleSecurityException(exception);

        // Assert
        assertNotNull(result);
        assertEquals(403, result.getStatus());
        assertEquals(expectedMessage, result.getDetail());
        assertEquals(expectedMessage, result.getProperties().get("description"));
        
        verify(messageSource).getMessage(eq("auth.error.account.expired"), isNull(), any(Locale.class));
    }

    @Test
    void handleSecurityException_DisabledException_WithPortugueseLocale_ShouldReturnPortugueseMessage() {
        // Arrange
        LocaleContextHolder.setLocale(new Locale("pt", "BR"));
        DisabledException exception = new DisabledException("User is disabled");
        String expectedMessage = "Sua conta está desabilitada. Entre em contato com o suporte para ativar sua conta.";
        
        when(messageSource.getMessage(eq("auth.error.user.disabled"), isNull(), any(Locale.class)))
                .thenReturn(expectedMessage);

        // Act
        ProblemDetail result = globalExceptionHandler.handleSecurityException(exception);

        // Assert
        assertNotNull(result);
        assertEquals(403, result.getStatus());
        assertEquals(expectedMessage, result.getDetail());
        assertEquals(expectedMessage, result.getProperties().get("description"));
        
        verify(messageSource).getMessage(eq("auth.error.user.disabled"), isNull(), any(Locale.class));
    }
}
