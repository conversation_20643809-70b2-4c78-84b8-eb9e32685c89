package ag.fuel.jobify.notification.service;

import ag.fuel.jobify.notification.entity.Notification;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.notification.repository.NotificationRepository;
import ag.fuel.jobify.user.repository.UserRepository;
import ag.fuel.jobify.notification.service.NotificationService;
import ag.fuel.jobify.user.service.UserService;
import ag.fuel.jobify.common.util.Constants;
import ag.fuel.jobify.common.config.BulkTestConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Random;

@SpringBootTest
@TestPropertySource(locations = "classpath:application-test-bulk.properties")
public class NotificationBulkInsertTest {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private BulkTestConfig bulkTestConfig;

    // Sample notification data arrays
    private static final String[] NOTIFICATION_TITLES = {
        "System Maintenance Scheduled",
        "New Feature Available",
        "Security Update Required",
        "Performance Improvement",
        "Database Backup Completed",
        "Server Migration Notice",
        "Policy Update Notification",
        "Welcome to New Version",
        "Important System Alert",
        "Scheduled Downtime Notice",
        "Bug Fix Released",
        "User Agreement Updated",
        "New Integration Available",
        "Backup Verification Complete",
        "System Health Check",
        "Network Upgrade Notice",
        "Data Migration Complete",
        "Security Patch Applied",
        "Service Restoration Notice",
        "Platform Update Available"
    };

    private static final String[] NOTIFICATION_SUBTITLES = {
        "Please review the changes and take necessary action.",
        "This update includes important security improvements.",
        "Your attention is required for this system notification.",
        "No action required from your side at this time.",
        "Please check your settings and update if necessary.",
        "This is an automated system notification.",
        "Review the details and contact support if needed.",
        "Important information regarding your account.",
        "System optimization has been completed successfully.",
        "Please save your work before the scheduled time.",
        "New functionality is now available for use.",
        "Your data has been processed successfully.",
        "Please verify your information is up to date.",
        "This notification is for your information only.",
        "Action may be required within the next 24 hours."
    };

    private static final String[] NOTIFICATION_ICONS = {
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-bell'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-alert-circle'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-info-circle'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-check-circle'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-settings'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-shield'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-database'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-server'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-tool'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-clock'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-bug'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-file-text'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-plug'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-backup'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-heart'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-network'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-transfer'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-patch'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-refresh'></i></span>",
        "<span class='avatar-initial rounded-circle bg-label-info'><i class='ti ti-upgrade'></i></span>"
    };

    @Test
    public void testBulkInsertSystemNotifications() {
        // Apply @Transactional conditionally based on configuration
        if (bulkTestConfig.isTransactional()) {
            testBulkInsertTransactional();
        } else {
            testBulkInsertDefinitive();
        }
    }

    @Transactional
    public void testBulkInsertTransactional() {
        System.out.println("[BULK_INSERT_TEST] Starting TRANSACTIONAL bulk insert of " + bulkTestConfig.getNotificationCount() + " notifications...");
        performBulkInsert();
    }

    public void testBulkInsertDefinitive() {
        System.out.println("[BULK_INSERT_TEST] Starting DEFINITIVE bulk insert of " + bulkTestConfig.getNotificationCount() + " notifications...");
        System.out.println("[BULK_INSERT_TEST] WARNING: These records will be PERMANENT!");
        performBulkInsert();
    }

    private void performBulkInsert() {
        // Get initial count
        long initialCount = notificationRepository.count();
        System.out.println("[BULK_INSERT_TEST] Initial notification count: " + initialCount);
        
        // Determine target user for notifications
        User targetUser = null;
        String notificationType = Constants.NOTIF_TYPE_SYSTEM;

        if (!bulkTestConfig.isSystemNotifications() && bulkTestConfig.getRealUserEmail() != null && !bulkTestConfig.getRealUserEmail().trim().isEmpty()) {
            // Look for the specified real user
            Optional<User> realUserOpt = userRepository.findByEmail(bulkTestConfig.getRealUserEmail());
            if (realUserOpt.isPresent()) {
                targetUser = realUserOpt.get();
                notificationType = Constants.NOTIF_TYPE_USER;
                System.out.println("[BULK_INSERT_TEST] Creating USER notifications for: " + targetUser.getEmail());
            } else {
                System.out.println("[BULK_INSERT_TEST] WARNING: Real user '" + bulkTestConfig.getRealUserEmail() + "' not found. Using SYSTEM notifications instead.");
            }
        }

        // Verify we have users in the system for SYSTEM notifications
        List<User> allUsers = userService.allUsers();
        System.out.println("[BULK_INSERT_TEST] Found " + allUsers.size() + " users in the system");

        if (allUsers.isEmpty() && bulkTestConfig.isCreateTestUsers()) {
            System.out.println("[BULK_INSERT_TEST] No users found. Creating " + bulkTestConfig.getTestUserCount() + " test users...");
            createTestUsers();
            allUsers = userService.allUsers();
        }

        Random random = new Random();
        long startTime = System.currentTimeMillis();

        // Create bulk notifications
        for (int i = 1; i <= bulkTestConfig.getNotificationCount(); i++) {
            String title = NOTIFICATION_TITLES[random.nextInt(NOTIFICATION_TITLES.length)] + " #" + i;
            String subtitle = NOTIFICATION_SUBTITLES[random.nextInt(NOTIFICATION_SUBTITLES.length)];
            String icon = NOTIFICATION_ICONS[random.nextInt(NOTIFICATION_ICONS.length)];

            try {
                notificationService.addNotification(
                    notificationType,
                    title,
                    subtitle,
                    icon,
                    targetUser // null for system notifications, specific user for user notifications
                );

                // Log progress based on configuration
                if (bulkTestConfig.isLogProgress() && i % bulkTestConfig.getBatchSize() == 0) {
                    System.out.println("[BULK_INSERT_TEST] Created " + i + "/" + bulkTestConfig.getNotificationCount() + " notifications...");
                }

            } catch (Exception e) {
                System.err.println("[BULK_INSERT_TEST] Error creating notification #" + i + ": " + e.getMessage());
            }
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // Verify final count
        long finalCount = notificationRepository.count();
        long createdCount = finalCount - initialCount;

        System.out.println("[BULK_INSERT_TEST] ===== BULK INSERT COMPLETED =====");
        System.out.println("[BULK_INSERT_TEST] Mode: " + (bulkTestConfig.isTransactional() ? "TRANSACTIONAL (will rollback)" : "DEFINITIVE (permanent)"));
        System.out.println("[BULK_INSERT_TEST] Type: " + notificationType + (targetUser != null ? " for " + targetUser.getEmail() : " for all users"));
        System.out.println("[BULK_INSERT_TEST] Initial count: " + initialCount);
        System.out.println("[BULK_INSERT_TEST] Final count: " + finalCount);
        System.out.println("[BULK_INSERT_TEST] Created: " + createdCount + " notifications");
        System.out.println("[BULK_INSERT_TEST] Target: " + bulkTestConfig.getNotificationCount() + " notifications");
        System.out.println("[BULK_INSERT_TEST] Duration: " + duration + "ms (" + (duration/1000.0) + " seconds)");
        System.out.println("[BULK_INSERT_TEST] Average: " + (duration/(double)bulkTestConfig.getNotificationCount()) + "ms per notification");
        
        // Verify that notifications were created for all users
        if (!allUsers.isEmpty()) {
            User firstUser = allUsers.get(0);
            long userNotificationCount = notificationService.getAllNotificationsForModal(firstUser).size();
            System.out.println("[BULK_INSERT_TEST] User '" + firstUser.getEmail() + "' has " + userNotificationCount + " notifications");
        }
    }

    @Test
    @Transactional
    public void testBulkInsertCustomCount() {
        // Example of how to use a different count
        int customCount = 50; // Smaller number for faster testing
        
        System.out.println("[CUSTOM_BULK_TEST] Creating " + customCount + " notifications for testing...");
        
        Random random = new Random();
        
        for (int i = 1; i <= customCount; i++) {
            String title = "Test Notification #" + i;
            String subtitle = "This is a test notification created for bulk testing purposes.";
            String icon = NOTIFICATION_ICONS[random.nextInt(NOTIFICATION_ICONS.length)];

            notificationService.addNotification(
                Constants.NOTIF_TYPE_SYSTEM,
                title,
                subtitle,
                icon,
                null
            );
        }
        
        System.out.println("[CUSTOM_BULK_TEST] Successfully created " + customCount + " test notifications");
    }

    /**
     * Helper method to create test users if none exist
     */
    private void createTestUsers() {
        for (int i = 1; i <= bulkTestConfig.getTestUserCount(); i++) {
            User testUser = new User();
            testUser.setFullName("Test User " + i);
            testUser.setEmail("test.bulk" + i + "@example.com");
            testUser.setPassword("password123");
            testUser.setEnabled(true);

            userRepository.save(testUser);
            System.out.println("[BULK_INSERT_TEST] Created test user: " + testUser.getEmail());
        }
    }

    /**
     * Utility method to clean up test notifications (use with caution!)
     */
    @Test
    @Transactional
    public void cleanupTestNotifications() {
        System.out.println("[CLEANUP_TEST] WARNING: This will delete ALL notifications!");
        System.out.println("[CLEANUP_TEST] Comment out this line if you really want to proceed:");
        //notificationRepository.deleteAll();
        System.out.println("[CLEANUP_TEST] Cleanup skipped for safety. Uncomment the line above to proceed.");
    }
}
