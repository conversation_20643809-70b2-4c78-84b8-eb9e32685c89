package ag.fuel.jobify.notification.controller;

import ag.fuel.jobify.notification.dto.CreateNotificationRequest;
import ag.fuel.jobify.notification.dto.NotificationDto;
import ag.fuel.jobify.notification.entity.Notification;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.notification.repository.NotificationRepository;
import ag.fuel.jobify.user.repository.UserRepository;
import ag.fuel.jobify.notification.service.NotificationService;
import ag.fuel.jobify.common.util.Constants;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
public class NotificationsControllerTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private UserRepository userRepository;

    @Test
    public void testCreateNotificationRequest_ShouldSerializeCorrectly() {
        // Arrange
        CreateNotificationRequest request = new CreateNotificationRequest(
            Constants.NOTIF_TYPE_SYSTEM, "Test Notification", "Test Subtitle", "test-icon.png", 1
        );

        // Act & Assert - This should not throw an exception
        assertDoesNotThrow(() -> {
            String json = objectMapper.writeValueAsString(request);
            System.out.println("[DEBUG_LOG] Successfully serialized CreateNotificationRequest: " + json);
            
            // Verify we can deserialize it back
            CreateNotificationRequest deserialized = objectMapper.readValue(json, CreateNotificationRequest.class);
            assertEquals(request.type(), deserialized.type());
            assertEquals(request.title(), deserialized.title());
        });
    }

    @Test
    public void testNotificationDto_ShouldSerializeCorrectly() {
        // Arrange
        NotificationDto dto = new NotificationDto(
            1L, Constants.NOTIF_TYPE_USER, "user-icon.png",
            "Test User Notification", "Test User Subtitle", new Date()
        );

        // Act & Assert - This should not throw an exception
        assertDoesNotThrow(() -> {
            String json = objectMapper.writeValueAsString(dto);
            System.out.println("[DEBUG_LOG] Successfully serialized NotificationDto: " + json);
            
            // Verify we can deserialize it back
            NotificationDto deserialized = objectMapper.readValue(json, NotificationDto.class);
            assertEquals(dto.id(), deserialized.id());
            assertEquals(dto.type(), deserialized.type());
        });
    }

    @Test
    public void testGetAllNotifications_ShouldReturnList() {
        // Act
        List<NotificationDto> notifications = notificationService.getAllNotifications();
        
        // Assert
        assertNotNull(notifications);
        System.out.println("[DEBUG_LOG] Retrieved " + notifications.size() + " notifications");
    }

    @Test
    public void testNotificationCreationAndDeletion_ShouldWork() {
        // This test verifies the basic flow without HTTP calls

        // Create a test user first to ensure SYSTEM notifications have users to assign to
        User testUser = createTestUserIfNotExists();

        // Test SYSTEM notification creation
        int initialCount = notificationService.getAllNotifications().size();

        notificationService.addNotification(
            Constants.NOTIF_TYPE_SYSTEM,
            "Test System Notification",
            "Test System Subtitle",
            "test-icon.png",
            null
        );

        int afterCreateCount = notificationService.getAllNotifications().size();
        assertEquals(initialCount + 1, afterCreateCount, "System notification should be created");

        // Find the created notification
        List<NotificationDto> notifications = notificationService.getAllNotifications();
        Optional<NotificationDto> createdNotification = notifications.stream()
            .filter(n -> "Test System Notification".equals(n.title()))
            .findFirst();

        assertTrue(createdNotification.isPresent(), "Created notification should be found");

        // Test deletion
        Long notificationId = createdNotification.get().id();
        int deletedCount = notificationService.deleteNotification(notificationId);

        System.out.println("[DEBUG_LOG] Deleted count: " + deletedCount);
        assertTrue(deletedCount > 0, "Notification should be deleted (deletedCount=" + deletedCount + ")");

        int afterDeleteCount = notificationService.getAllNotifications().size();
        assertEquals(initialCount, afterDeleteCount, "Notification count should return to initial value");

        System.out.println("[DEBUG_LOG] Successfully tested notification creation and deletion flow");
    }

    private User createTestUserIfNotExists() {
        Optional<User> existingUser = userRepository.findByEmail("<EMAIL>");
        if (existingUser.isPresent()) {
            return existingUser.get();
        }

        // Create a test user
        User testUser = new User();
        testUser.setEmail("<EMAIL>");
        testUser.setFullName("Test User");
        testUser.setPassword("password123"); // This will be encoded by the service
        testUser.setEnabled(true);
        testUser.setAccountLocked(false);

        return userRepository.save(testUser);
    }
}
