package ag.fuel.jobify.user.service;

import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.auth.entity.Role;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.auth.repository.RoleRepository;
import ag.fuel.jobify.user.repository.UserRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.ANY) // Use H2 in-memory database for tests
@Transactional // Rolls back transactions after each test
public class UserRoleIntegrationTest {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Test
    void testUserHasAdminRole() {
        // Ensure ROLE_ADMIN exists or create it
        Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
                .orElseGet(() -> {
                    Role newAdminRole = new Role();
                    newAdminRole.setName(ERole.ROLE_ADMIN);
                    return roleRepository.save(newAdminRole);
                });

        // Create a new User
        User user = new User();
        user.setFullName("Usuário Teste Admin");
        user.setEmail("<EMAIL>");
        // It's good practice to encode passwords, even in tests, if your system expects encoded passwords.
        user.setPassword(passwordEncoder.encode("TestPassword123!"));
        user.setEnabled(true); // Make sure the user account is enabled

        // Assign ADMIN role to the user
        Set<Role> roles = new HashSet<>();
        roles.add(adminRole);
        user.setRoles(roles);

        // Save the user
        User savedUser = userRepository.save(user);
        assertNotNull(savedUser.getId(), "User ID should not be null after saving");
        assertEquals("<EMAIL>", savedUser.getEmail(), "Email should match");

        // Retrieve the user from the database to ensure changes are persisted and correctly fetched
        Optional<User> retrievedUserOptional = userRepository.findByEmail("<EMAIL>");
        assertTrue(retrievedUserOptional.isPresent(), "User should be found by email");

        User retrievedUser = retrievedUserOptional.get();

        // Assert that the retrieved user has the ROLE_ADMIN
        assertNotNull(retrievedUser.getRoles(), "User roles should not be null");
        assertFalse(retrievedUser.getRoles().isEmpty(), "User roles should not be empty");

        boolean isAdminByRoleEntity = retrievedUser.getRoles().stream()
                .anyMatch(role -> role.getName().equals(ERole.ROLE_ADMIN));
        assertTrue(isAdminByRoleEntity, "User should have the ADMIN role (checked via Role entity)");

        // Assert that the user has the 'ROLE_ADMIN' authority (as Spring Security would see it)
        // The User entity's getAuthorities() method should convert ERole.ROLE_ADMIN to "ROLE_ADMIN"
        boolean hasAdminAuthority = retrievedUser.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authorityString -> authorityString.equals("ROLE_ADMIN"));
        assertTrue(hasAdminAuthority, "User should have ROLE_ADMIN authority");
    }
}
