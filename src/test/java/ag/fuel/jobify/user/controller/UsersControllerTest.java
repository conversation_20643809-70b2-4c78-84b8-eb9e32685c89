package ag.fuel.jobify.user.controller;

import ag.fuel.jobify.user.entity.User;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@SpringBootTest
public class UsersControllerTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testUserSerialization_ShouldNotThrowLazyInitializationException() {
        // Arrange
        User user = new User();
        user.setId(1);
        user.setEmail("<EMAIL>");
        user.setFullName("Test User");

        // Act & Assert - This should not throw a LazyInitializationException
        assertDoesNotThrow(() -> {
            String json = objectMapper.writeValueAsString(user);
            System.out.println("[DEBUG_LOG] Successfully serialized User object: " + json);
        });
    }
}
