package ag.fuel.jobify.reference.service;

import ag.fuel.jobify.reference.entity.Version;
import ag.fuel.jobify.reference.repository.VersionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VersionServiceTest {

    @Mock
    private VersionRepository versionRepository;

    @InjectMocks
    private VersionService versionService;

    private List<Version> testVersions;

    @BeforeEach
    void setUp() {
        // Create test versions in random order
        Version v1_0_0 = new Version();
        v1_0_0.setId(1L);
        v1_0_0.setLabel("1.0.0");

        Version v2_1_3 = new Version();
        v2_1_3.setId(2L);
        v2_1_3.setLabel("2.1.3");

        Version v1_2_0 = new Version();
        v1_2_0.setId(3L);
        v1_2_0.setLabel("1.2.0");

        Version v2_0_1 = new Version();
        v2_0_1.setId(4L);
        v2_0_1.setLabel("2.0.1");

        Version v1_0_5 = new Version();
        v1_0_5.setId(5L);
        v1_0_5.setLabel("1.0.5");

        // Add in random order to test sorting
        testVersions = Arrays.asList(v1_0_0, v2_1_3, v1_2_0, v2_0_1, v1_0_5);
    }

    @Test
    void testGetAllVersions_ShouldReturnVersionsInDescendingOrder() {
        // Given
        when(versionRepository.findAllVersions()).thenReturn(testVersions);

        // When
        List<Version> result = versionService.getAllVersions();

        // Then
        assertNotNull(result);
        assertEquals(5, result.size());
        
        // Verify descending order (newest first)
        assertEquals("2.1.3", result.get(0).getLabel());
        assertEquals("2.0.1", result.get(1).getLabel());
        assertEquals("1.2.0", result.get(2).getLabel());
        assertEquals("1.0.5", result.get(3).getLabel());
        assertEquals("1.0.0", result.get(4).getLabel());
    }

    @Test
    void testGetCurrentVersion_ShouldReturnLatestVersion() {
        // Given
        when(versionRepository.findAllVersions()).thenReturn(testVersions);

        // When
        Version result = versionService.getCurrentVersion();

        // Then
        assertNotNull(result);
        assertEquals("2.1.3", result.getLabel());
    }

    @Test
    void testIsVersionBGreaterThanVersionA() {
        // Test various version comparisons
        assertTrue(versionService.isVersionBGreaterThanVersionA("1.0.0", "1.0.1"));
        assertTrue(versionService.isVersionBGreaterThanVersionA("1.0.0", "1.1.0"));
        assertTrue(versionService.isVersionBGreaterThanVersionA("1.0.0", "2.0.0"));

        assertFalse(versionService.isVersionBGreaterThanVersionA("1.0.1", "1.0.0"));
        assertFalse(versionService.isVersionBGreaterThanVersionA("1.1.0", "1.0.0"));
        assertFalse(versionService.isVersionBGreaterThanVersionA("2.0.0", "1.0.0"));

        assertFalse(versionService.isVersionBGreaterThanVersionA("1.0.0", "1.0.0"));
    }

    @Test
    void testDeleteVersion() {
        // Given
        Version versionToDelete = testVersions.get(2); // 1.2.0

        // When
        versionService.deleteVersion(versionToDelete.getId());

        // Then
        // Verify that deleteById was called with the correct ID
        org.mockito.Mockito.verify(versionRepository).deleteById(versionToDelete.getId());
    }
}
