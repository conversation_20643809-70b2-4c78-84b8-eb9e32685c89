package ag.fuel.jobify.company.service;

import ag.fuel.jobify.company.dto.CompanyDto;
import ag.fuel.jobify.company.entity.Company;
import ag.fuel.jobify.company.repository.CompanyRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CompanyServiceTest {

    @Mock
    private CompanyRepository companyRepository;

    @InjectMocks
    private CompanyService companyService;

    private Company testCompany;
    private CompanyDto testCompanyDto;

    @BeforeEach
    void setUp() {
        testCompany = new Company();
        testCompany.setId(1L);
        testCompany.setCompanyName("Test Company");
        testCompany.setAddress("123 Test Street");
        testCompany.setCountry("Test Country");
        testCompany.setTaxNumber("TAX123456");
        testCompany.setDomainName("test.com");

        testCompanyDto = new CompanyDto(
                1L,
                "Test Company",
                "123 Test Street",
                "Test Country",
                "TAX123456",
                "test.com"
        );
    }

    @Test
    void getAllCompanies_ShouldReturnPagedCompanies() {
        // Given
        Pageable pageable = PageRequest.of(0, 20);
        List<Company> companies = Arrays.asList(testCompany);
        Page<Company> companyPage = new PageImpl<>(companies, pageable, 1);

        when(companyRepository.findAllByOrderByCompanyNameAsc(pageable)).thenReturn(companyPage);

        // When
        Page<Company> result = companyService.getAllCompanies(pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("Test Company", result.getContent().get(0).getCompanyName());
        verify(companyRepository).findAllByOrderByCompanyNameAsc(pageable);
    }

    @Test
    void getCompanyById_ShouldReturnCompany_WhenExists() {
        // Given
        when(companyRepository.findById(1L)).thenReturn(Optional.of(testCompany));

        // When
        Optional<Company> result = companyService.getCompanyById(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals("Test Company", result.get().getCompanyName());
        verify(companyRepository).findById(1L);
    }

    @Test
    void createCompany_ShouldCreateCompany_WhenTaxNumberIsUnique() {
        // Given
        when(companyRepository.existsByTaxNumber("TAX123456")).thenReturn(false);
        when(companyRepository.save(any(Company.class))).thenReturn(testCompany);

        // When
        Company result = companyService.createCompany(testCompanyDto);

        // Then
        assertNotNull(result);
        assertEquals("Test Company", result.getCompanyName());
        verify(companyRepository).existsByTaxNumber("TAX123456");
        verify(companyRepository).save(any(Company.class));
    }

    @Test
    void createCompany_ShouldThrowException_WhenTaxNumberExists() {
        // Given
        when(companyRepository.existsByTaxNumber("TAX123456")).thenReturn(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> companyService.createCompany(testCompanyDto)
        );

        assertEquals("Company with TAX number TAX123456 already exists", exception.getMessage());
        verify(companyRepository).existsByTaxNumber("TAX123456");
        verify(companyRepository, never()).save(any(Company.class));
    }

    @Test
    void updateCompany_ShouldUpdateCompany_WhenExists() {
        // Given
        when(companyRepository.findById(1L)).thenReturn(Optional.of(testCompany));
        when(companyRepository.existsByTaxNumberAndIdNot("TAX123456", 1L)).thenReturn(false);
        when(companyRepository.save(any(Company.class))).thenReturn(testCompany);

        // When
        Company result = companyService.updateCompany(1L, testCompanyDto);

        // Then
        assertNotNull(result);
        assertEquals("Test Company", result.getCompanyName());
        verify(companyRepository).findById(1L);
        verify(companyRepository).existsByTaxNumberAndIdNot("TAX123456", 1L);
        verify(companyRepository).save(any(Company.class));
    }

    @Test
    void updateCompany_ShouldThrowException_WhenCompanyNotFound() {
        // Given
        when(companyRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> companyService.updateCompany(1L, testCompanyDto)
        );

        assertEquals("Company not found with ID: 1", exception.getMessage());
        verify(companyRepository).findById(1L);
        verify(companyRepository, never()).save(any(Company.class));
    }

    @Test
    void deleteCompany_ShouldDeleteCompany_WhenExists() {
        // Given
        when(companyRepository.existsById(1L)).thenReturn(true);

        // When
        companyService.deleteCompany(1L);

        // Then
        verify(companyRepository).existsById(1L);
        verify(companyRepository).deleteById(1L);
    }

    @Test
    void deleteCompany_ShouldThrowException_WhenCompanyNotFound() {
        // Given
        when(companyRepository.existsById(1L)).thenReturn(false);

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> companyService.deleteCompany(1L)
        );

        assertEquals("Company not found with ID: 1", exception.getMessage());
        verify(companyRepository).existsById(1L);
        verify(companyRepository, never()).deleteById(1L);
    }

    @Test
    void getDistinctCountries_ShouldReturnCountries() {
        // Given
        List<String> countries = Arrays.asList("Country A", "Country B");
        when(companyRepository.findDistinctCountries()).thenReturn(countries);

        // When
        List<String> result = companyService.getDistinctCountries();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("Country A"));
        assertTrue(result.contains("Country B"));
        verify(companyRepository).findDistinctCountries();
    }

    @Test
    void toDto_ShouldConvertCompanyToDto() {
        // When
        CompanyDto result = companyService.toDto(testCompany);

        // Then
        assertNotNull(result);
        assertEquals(testCompany.getId(), result.id());
        assertEquals(testCompany.getCompanyName(), result.companyName());
        assertEquals(testCompany.getAddress(), result.address());
        assertEquals(testCompany.getCountry(), result.country());
        assertEquals(testCompany.getTaxNumber(), result.taxNumber());
        assertEquals(testCompany.getDomainName(), result.domainName());
    }
}
