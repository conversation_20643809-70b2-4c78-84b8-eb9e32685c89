package ag.fuel.jobify.security.service;

import ag.fuel.jobify.security.entity.PasswordHistory;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.security.service.PasswordPolicyService;
import ag.fuel.jobify.security.repository.PasswordHistoryRepository;
import ag.fuel.jobify.user.repository.UserRepository;
import ag.fuel.jobify.security.service.PasswordValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PasswordPolicyServiceTest {

    @Mock
    private PasswordHistoryRepository passwordHistoryRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private PasswordValidator passwordValidator;

    private PasswordPolicyService passwordPolicyService;

    @BeforeEach
    void setUp() {
        passwordPolicyService = new PasswordPolicyService(
                passwordHistoryRepository,
                userRepository,
                passwordEncoder,
                passwordValidator
        );

        // Set default values for configuration properties
        ReflectionTestUtils.setField(passwordPolicyService, "passwordHistoryCount", 5);
        ReflectionTestUtils.setField(passwordPolicyService, "passwordExpiryDays", 90);
    }

    @Test
    void validatePassword_ValidPassword_ReturnsNoErrors() {
        // Arrange
        String password = "ValidPass123!";
        User user = new User();
        when(passwordValidator.validatePassword(password)).thenReturn(Collections.emptyList());
        when(passwordHistoryRepository.findByUserOrderByCreatedAtDesc(user)).thenReturn(Collections.emptyList());

        // Act
        List<String> errors = passwordPolicyService.validatePassword(password, user);

        // Assert
        assertTrue(errors.isEmpty());
        verify(passwordValidator).validatePassword(password);
        verify(passwordHistoryRepository).findByUserOrderByCreatedAtDesc(user);
    }

    @Test
    void validatePassword_PasswordInHistory_ReturnsError() {
        // Arrange
        String password = "HistoryPass123!";
        User user = new User();
        PasswordHistory history = new PasswordHistory();
        history.setPasswordHash("hashedPassword");

        when(passwordValidator.validatePassword(password)).thenReturn(Collections.emptyList());
        when(passwordHistoryRepository.findByUserOrderByCreatedAtDesc(user))
                .thenReturn(Collections.singletonList(history));
        when(passwordEncoder.matches(password, "hashedPassword")).thenReturn(true);

        // Act
        List<String> errors = passwordPolicyService.validatePassword(password, user);

        // Assert
        assertFalse(errors.isEmpty());
        assertEquals(1, errors.size());
        assertTrue(errors.get(0).contains("Password has been used recently"));
    }

    @Test
    void isPasswordInHistory_PasswordExists_ReturnsTrue() {
        // Arrange
        String password = "OldPass123!";
        User user = new User();
        PasswordHistory history = new PasswordHistory();
        history.setPasswordHash("hashedOldPassword");

        when(passwordHistoryRepository.findByUserOrderByCreatedAtDesc(user))
                .thenReturn(Collections.singletonList(history));
        when(passwordEncoder.matches(password, "hashedOldPassword")).thenReturn(true);

        // Act
        boolean result = passwordPolicyService.isPasswordInHistory(password, user);

        // Assert
        assertTrue(result);
    }

    @Test
    void updatePassword_ValidPassword_UpdatesSuccessfully() {
        // Arrange
        String newPassword = "NewValidPass123!";
        User user = new User();
        user.setPassword("oldPasswordHash");

        when(passwordValidator.validatePassword(newPassword)).thenReturn(Collections.emptyList());
        when(passwordHistoryRepository.findByUserOrderByCreatedAtDesc(user)).thenReturn(Collections.emptyList());
        when(passwordEncoder.encode(newPassword)).thenReturn("newPasswordHash");
        when(userRepository.save(any(User.class))).thenAnswer(i -> i.getArguments()[0]);
        when(passwordHistoryRepository.save(any(PasswordHistory.class))).thenAnswer(i -> i.getArguments()[0]);

        // Act
        User updatedUser = passwordPolicyService.updatePassword(user, newPassword);

        // Assert
        assertNotNull(updatedUser);
        assertEquals("newPasswordHash", updatedUser.getPassword());
        assertNotNull(updatedUser.getPasswordChangedDate());
        assertNotNull(updatedUser.getPasswordExpiryDate());
        verify(userRepository).save(user);
        verify(passwordHistoryRepository).save(any(PasswordHistory.class));
    }

    @Test
    void trimPasswordHistory_ExceedsLimit_TrimsHistory() {
        // Arrange
        User user = new User();
        List<PasswordHistory> history = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            history.add(new PasswordHistory());
        }

        when(passwordHistoryRepository.countByUser(user)).thenReturn((long) history.size());
        when(passwordHistoryRepository.findByUserOrderByCreatedAtDesc(user)).thenReturn(history);

        // Act
        passwordPolicyService.trimPasswordHistory(user);

        // Assert
        verify(passwordHistoryRepository, times(2)).delete(any(PasswordHistory.class));
    }

    @Test
    void isPasswordExpired_ExpiredPassword_ReturnsTrue() {
        // Arrange
        User user = new User();
        LocalDateTime pastDate = LocalDateTime.now().minusDays(91);
        user.setPasswordExpiryDate(pastDate);

        // Act
        boolean result = passwordPolicyService.isPasswordExpired(user);

        // Assert
        assertTrue(result);
    }

    @Test
    void isPasswordExpired_ValidPassword_ReturnsFalse() {
        // Arrange
        User user = new User();
        LocalDateTime futureDate = LocalDateTime.now().plusDays(30);
        user.setPasswordExpiryDate(futureDate);

        // Act
        boolean result = passwordPolicyService.isPasswordExpired(user);

        // Assert
        assertFalse(result);
    }

    @Test
    void setInitialPasswordExpiry_NewUser_SetsCorrectDates() {
        // Arrange
        User user = new User();
        when(userRepository.save(any(User.class))).thenAnswer(i -> i.getArguments()[0]);

        // Act
        User updatedUser = passwordPolicyService.setInitialPasswordExpiry(user);

        // Assert
        assertNotNull(updatedUser.getPasswordChangedDate());
        assertNotNull(updatedUser.getPasswordExpiryDate());
        assertTrue(updatedUser.getPasswordExpiryDate()
                .isAfter(LocalDateTime.now().plusDays(89))); // At least 89 days in future
        verify(userRepository).save(user);
    }

    @Test
    void updatePassword_InvalidPassword_ThrowsException() {
        // Arrange
        String newPassword = "invalid";
        User user = new User();
        List<String> validationErrors = Arrays.asList("Password too short", "Password needs special characters");

        when(passwordValidator.validatePassword(newPassword)).thenReturn(validationErrors);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> passwordPolicyService.updatePassword(user, newPassword));

        assertTrue(exception.getMessage().contains("Password validation failed"));
        verify(userRepository, never()).save(any());
    }
}
