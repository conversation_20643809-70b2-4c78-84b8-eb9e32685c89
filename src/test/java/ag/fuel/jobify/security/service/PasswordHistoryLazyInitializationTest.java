package ag.fuel.jobify.security.service;

import ag.fuel.jobify.security.entity.PasswordHistory;
import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.security.repository.PasswordHistoryRepository;
import ag.fuel.jobify.user.repository.UserRepository;
import ag.fuel.jobify.security.service.PasswordValidator;
import ag.fuel.jobify.security.service.PasswordPolicyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test to verify that the lazy initialization issue with passwordHistory collection is fixed.
 */
@ExtendWith(MockitoExtension.class)
class PasswordHistoryLazyInitializationTest {

    @Mock
    private PasswordHistoryRepository passwordHistoryRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private PasswordValidator passwordValidator;

    private PasswordPolicyService passwordPolicyService;

    @BeforeEach
    void setUp() {
        passwordPolicyService = new PasswordPolicyService(
                passwordHistoryRepository,
                userRepository,
                passwordEncoder,
                passwordValidator
        );

        // Set default values for configuration properties
        ReflectionTestUtils.setField(passwordPolicyService, "passwordHistoryCount", 5);
        ReflectionTestUtils.setField(passwordPolicyService, "passwordExpiryDays", 90);
    }

    @Test
    void updatePassword_ShouldNotCauseLazyInitializationException() {
        // Arrange
        String newPassword = "NewValidPass123!";
        User user = new User();
        user.setId(1);
        user.setPassword("oldPasswordHash");

        // Mock the dependencies to avoid lazy initialization issues
        when(passwordValidator.validatePassword(newPassword)).thenReturn(Collections.emptyList());
        when(passwordHistoryRepository.findByUserOrderByCreatedAtDesc(user)).thenReturn(Collections.emptyList());
        when(passwordEncoder.encode(newPassword)).thenReturn("newPasswordHash");
        when(userRepository.save(any(User.class))).thenAnswer(i -> i.getArguments()[0]);
        when(passwordHistoryRepository.save(any(PasswordHistory.class))).thenAnswer(i -> i.getArguments()[0]);
        when(passwordHistoryRepository.countByUser(user)).thenReturn(0L);

        // Act - This should not throw a LazyInitializationException
        assertDoesNotThrow(() -> {
            User updatedUser = passwordPolicyService.updatePassword(user, newPassword);
            assertNotNull(updatedUser);
        });

        // Assert
        verify(passwordHistoryRepository).save(any(PasswordHistory.class));
        verify(userRepository).save(user);
    }

    @Test
    void updatePassword_ShouldSavePasswordHistoryDirectlyToRepository() {
        // Arrange
        String newPassword = "NewValidPass123!";
        User user = new User();
        user.setId(1);
        user.setPassword("oldPasswordHash");

        when(passwordValidator.validatePassword(newPassword)).thenReturn(Collections.emptyList());
        when(passwordHistoryRepository.findByUserOrderByCreatedAtDesc(user)).thenReturn(Collections.emptyList());
        when(passwordEncoder.encode(newPassword)).thenReturn("newPasswordHash");
        when(userRepository.save(any(User.class))).thenAnswer(i -> i.getArguments()[0]);
        when(passwordHistoryRepository.save(any(PasswordHistory.class))).thenAnswer(i -> i.getArguments()[0]);
        when(passwordHistoryRepository.countByUser(user)).thenReturn(0L);

        // Act
        passwordPolicyService.updatePassword(user, newPassword);

        // Assert - Verify that password history is saved directly to repository
        verify(passwordHistoryRepository).save(argThat(passwordHistory -> 
            passwordHistory.getUser().equals(user) && 
            passwordHistory.getPasswordHash().equals("oldPasswordHash")
        ));
    }
}
