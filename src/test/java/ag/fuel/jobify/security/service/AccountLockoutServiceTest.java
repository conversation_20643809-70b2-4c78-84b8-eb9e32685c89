package ag.fuel.jobify.security.service;

import ag.fuel.jobify.user.entity.User;
import ag.fuel.jobify.user.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SpringBootTest
class AccountLockoutServiceTest {

    @MockBean
    private UserRepository userRepository;

    @Autowired
    private AccountLockoutService accountLockoutService;

    private static final String TEST_EMAIL = "<EMAIL>";
    private User testUser;

    @BeforeEach
    void setUp() {
        // Set up test user
        testUser = new User();
        testUser.setEmail(TEST_EMAIL);
        testUser.setFailedAttempt(0);
        testUser.setAccountLocked(false);
        testUser.setLockTime(null);

        // Mock UserRepository behavior
        when(userRepository.findByEmail(TEST_EMAIL)).thenReturn(Optional.of(testUser));
    }

    @Test
    void incrementFailedAttempts_ShouldWork() {
        // Act
        accountLockoutService.incrementFailedAttempts(TEST_EMAIL);

        // Assert
        verify(userRepository).findByEmail(TEST_EMAIL);
        // The service should work with either JPQL or repository fallback
    }

    @Test
    void incrementFailedAttempts_WithNullEmail_ShouldReturnEarly() {
        // Act
        accountLockoutService.incrementFailedAttempts(null);

        // Assert - should not interact with repository for null email
        verify(userRepository, never()).findByEmail(anyString());
    }

    @Test
    void incrementFailedAttempts_WithNonExistentEmail_ShouldReturnEarly() {
        // Arrange
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.empty());

        // Act
        accountLockoutService.incrementFailedAttempts("<EMAIL>");

        // Assert
        verify(userRepository).findByEmail("<EMAIL>");
    }

    @Test
    void lockAccount_ShouldWork() {
        // Act
        accountLockoutService.lockAccount(TEST_EMAIL);

        // Assert - the service should work with either JPQL or repository fallback
        // We can't easily test the internal implementation details in integration test
    }

    @Test
    void lockAccount_WithNullEmail_ShouldReturnEarly() {
        // Act
        accountLockoutService.lockAccount(null);

        // Assert - should handle null email gracefully
        // No specific verification needed as the method should return early
    }

    @Test
    void unlockAccount_ShouldWork() {
        // Act
        accountLockoutService.unlockAccount(TEST_EMAIL);

        // Assert - the service should work with either JPQL or repository fallback
    }

    @Test
    void unlockAccount_WithNullEmail_ShouldReturnEarly() {
        // Act
        accountLockoutService.unlockAccount(null);

        // Assert - should handle null email gracefully
    }

    @Test
    void resetFailedAttempts_ShouldWork() {
        // Act
        accountLockoutService.resetFailedAttempts(TEST_EMAIL);

        // Assert - the service should work with either JPQL or repository fallback
    }

    @Test
    void resetFailedAttempts_WithNullEmail_ShouldReturnEarly() {
        // Act
        accountLockoutService.resetFailedAttempts(null);

        // Assert - should handle null email gracefully
    }

    @Test
    void isAccountLocked_WhenAccountIsLocked_ShouldReturnTrue() {
        // Arrange
        testUser.setAccountLocked(true);
        testUser.setLockTime(new java.util.Date()); // Recent lock time

        // Act
        boolean result = accountLockoutService.isAccountLocked(TEST_EMAIL);

        // Assert
        assertTrue(result);
    }

    @Test
    void isAccountLocked_WhenAccountIsNotLocked_ShouldReturnFalse() {
        // Arrange
        testUser.setAccountLocked(false);

        // Act
        boolean result = accountLockoutService.isAccountLocked(TEST_EMAIL);

        // Assert
        assertFalse(result);
    }

    @Test
    void isAccountLocked_WithNullEmail_ShouldReturnFalse() {
        // Act
        boolean result = accountLockoutService.isAccountLocked(null);

        // Assert
        assertFalse(result);
    }
}
