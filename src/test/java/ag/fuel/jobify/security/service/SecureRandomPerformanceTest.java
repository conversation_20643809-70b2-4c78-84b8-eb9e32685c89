package ag.fuel.jobify.security.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.security.SecureRandom;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify SecureRandom performance optimizations.
 */
@SpringBootTest
@ActiveProfiles("test")
class SecureRandomPerformanceTest {

    @Autowired
    private SecureRandom secureRandom;

    @Autowired
    private SecureTokenGenerator secureTokenGenerator;

    @Test
    void testSecureRandomPerformance() {
        // Test that SecureRandom is properly configured and performs well
        long startTime = System.currentTimeMillis();
        
        // Generate multiple random values to test performance
        for (int i = 0; i < 100; i++) {
            byte[] randomBytes = new byte[16];
            secureRandom.nextBytes(randomBytes);
            assertNotNull(randomBytes);
            assertEquals(16, randomBytes.length);
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // Should complete in reasonable time (less than 1 second for 100 iterations)
        assertTrue(duration < 1000, "SecureRandom generation took too long: " + duration + "ms");
        
        System.out.println("SecureRandom performance test completed in " + duration + "ms");
    }

    @Test
    void testSecureTokenGenerator() {
        // Test that SecureTokenGenerator works correctly
        long startTime = System.currentTimeMillis();
        
        String token1 = secureTokenGenerator.generateSecureToken();
        String token2 = secureTokenGenerator.generateSecureToken();
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // Verify tokens are generated
        assertNotNull(token1);
        assertNotNull(token2);
        assertNotEquals(token1, token2);
        
        // Verify UUID format (36 characters with hyphens)
        assertEquals(36, token1.length());
        assertEquals(36, token2.length());
        assertTrue(token1.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
        assertTrue(token2.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
        
        // Should complete quickly
        assertTrue(duration < 100, "Token generation took too long: " + duration + "ms");
        
        System.out.println("SecureTokenGenerator test completed in " + duration + "ms");
    }

    @Test
    void testSecureTokenGeneratorWithCustomLength() {
        // Test custom length token generation
        String token = secureTokenGenerator.generateSecureToken(32);
        
        assertNotNull(token);
        assertEquals(64, token.length()); // 32 bytes = 64 hex characters
        assertTrue(token.matches("[0-9a-f]{64}"));
        
        System.out.println("Generated custom length token: " + token);
    }
}
