package ag.fuel.jobify.security.handler;

import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;

import static org.mockito.Mockito.*;

/**
 * Test class for CustomAccessDeniedHandler.
 * Tests the handler's ability to differentiate between API and web requests
 * and respond appropriately.
 */
class CustomAccessDeniedHandlerTest {

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private AccessDeniedException accessDeniedException;

    @Mock
    private Authentication authentication;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private RequestDispatcher requestDispatcher;

    @InjectMocks
    private CustomAccessDeniedHandler handler;

    private StringWriter stringWriter;
    private PrintWriter printWriter;

    @BeforeEach
    void setUp() throws IOException {
        MockitoAnnotations.openMocks(this);
        
        // Set up SecurityContext
        SecurityContextHolder.setContext(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        
        // Set up PrintWriter for response
        stringWriter = new StringWriter();
        printWriter = new PrintWriter(stringWriter);
        when(response.getWriter()).thenReturn(printWriter);

        // Set up RequestDispatcher
        when(request.getRequestDispatcher("/error")).thenReturn(requestDispatcher);

        // Set up default exception message
        when(accessDeniedException.getMessage()).thenReturn("Access is denied");
    }

    @Test
    void handle_WithJsonAcceptHeader_ShouldReturnJsonResponse() throws IOException, ServletException {
        // Arrange
        when(request.getHeader("Accept")).thenReturn("application/json");
        when(request.getRequestURI()).thenReturn("/admin/users");

        // Act
        handler.handle(request, response, accessDeniedException);

        // Assert
        verify(response).setStatus(HttpServletResponse.SC_FORBIDDEN);
        verify(response).setContentType("application/json");
        verify(response).getWriter();
        
        String jsonResponse = stringWriter.toString();
        assert jsonResponse.contains("\"error\":\"Access Denied\"");
        assert jsonResponse.contains("\"message\":\"Access is denied\"");
    }

    @Test
    void handle_WithJsonContentType_ShouldReturnJsonResponse() throws IOException, ServletException {
        // Arrange
        when(request.getHeader("Content-Type")).thenReturn("application/json");
        when(request.getRequestURI()).thenReturn("/admin/users");

        // Act
        handler.handle(request, response, accessDeniedException);

        // Assert
        verify(response).setStatus(HttpServletResponse.SC_FORBIDDEN);
        verify(response).setContentType("application/json");
        verify(response).getWriter();
    }

    @Test
    void handle_WithApiPath_ShouldReturnJsonResponse() throws IOException, ServletException {
        // Arrange
        when(request.getRequestURI()).thenReturn("/api/admin/users");

        // Act
        handler.handle(request, response, accessDeniedException);

        // Assert
        verify(response).setStatus(HttpServletResponse.SC_FORBIDDEN);
        verify(response).setContentType("application/json");
        verify(response).getWriter();
    }

    @Test
    void handle_WithWebRequest_ShouldForwardToErrorPage() throws IOException, ServletException {
        // Arrange
        when(request.getHeader("Accept")).thenReturn("text/html,application/xhtml+xml");
        when(request.getRequestURI()).thenReturn("/admin/users");

        // Act
        handler.handle(request, response, accessDeniedException);

        // Assert
        verify(request).setAttribute(RequestDispatcher.ERROR_STATUS_CODE, HttpStatus.FORBIDDEN.value());
        verify(request).setAttribute(RequestDispatcher.ERROR_REQUEST_URI, "/admin/users");
        verify(request).setAttribute(RequestDispatcher.ERROR_EXCEPTION, accessDeniedException);
        verify(requestDispatcher).forward(request, response);
        verify(response, never()).setContentType("application/json");
        verify(response, never()).getWriter();
    }

    @Test
    void handle_WithNoSpecialHeaders_ShouldForwardToErrorPage() throws IOException, ServletException {
        // Arrange
        when(request.getRequestURI()).thenReturn("/admin/users");

        // Act
        handler.handle(request, response, accessDeniedException);

        // Assert
        verify(request).setAttribute(RequestDispatcher.ERROR_STATUS_CODE, HttpStatus.FORBIDDEN.value());
        verify(request).setAttribute(RequestDispatcher.ERROR_REQUEST_URI, "/admin/users");
        verify(request).setAttribute(RequestDispatcher.ERROR_EXCEPTION, accessDeniedException);
        verify(requestDispatcher).forward(request, response);
        verify(response, never()).setContentType("application/json");
        verify(response, never()).getWriter();
    }

    @Test
    void handle_WithAuthenticatedUser_ShouldLogUserDetails() throws IOException, ServletException {
        // Arrange
        when(request.getRequestURI()).thenReturn("/admin/users");
        when(authentication.getName()).thenReturn("<EMAIL>");
        when(authentication.getAuthorities()).thenReturn(null);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getPrincipal()).thenReturn(new Object());

        // Act
        handler.handle(request, response, accessDeniedException);

        // Assert
        verify(request).setAttribute(RequestDispatcher.ERROR_STATUS_CODE, HttpStatus.FORBIDDEN.value());
        verify(request).setAttribute(RequestDispatcher.ERROR_REQUEST_URI, "/admin/users");
        verify(request).setAttribute(RequestDispatcher.ERROR_EXCEPTION, accessDeniedException);
        verify(requestDispatcher).forward(request, response);
        // Verify that authentication details are accessed for logging
        verify(authentication).getName();
        verify(authentication).getAuthorities();
        verify(authentication).isAuthenticated();
        verify(authentication).getPrincipal();
    }

    @Test
    void handle_WithNullAuthentication_ShouldHandleGracefully() throws IOException, ServletException {
        // Arrange
        when(securityContext.getAuthentication()).thenReturn(null);
        when(request.getRequestURI()).thenReturn("/admin/users");

        // Act
        handler.handle(request, response, accessDeniedException);

        // Assert
        verify(request).setAttribute(RequestDispatcher.ERROR_STATUS_CODE, HttpStatus.FORBIDDEN.value());
        verify(request).setAttribute(RequestDispatcher.ERROR_REQUEST_URI, "/admin/users");
        verify(request).setAttribute(RequestDispatcher.ERROR_EXCEPTION, accessDeniedException);
        verify(requestDispatcher).forward(request, response);
    }
}
