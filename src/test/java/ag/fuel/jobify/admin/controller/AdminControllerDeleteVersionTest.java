package ag.fuel.jobify.admin.controller;

import ag.fuel.jobify.admin.controller.AdminController;
import ag.fuel.jobify.notification.service.NotificationService;
import ag.fuel.jobify.reference.entity.Version;
import ag.fuel.jobify.reference.service.VersionService;
import ag.fuel.jobify.user.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class AdminControllerDeleteVersionTest {

    @Mock
    private VersionService versionService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private UserService userService;

    @InjectMocks
    private AdminController adminController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private List<Version> testVersions;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(adminController).build();
        objectMapper = new ObjectMapper();

        // Create test versions
        Version v1_0_0 = new Version();
        v1_0_0.setId(1L);
        v1_0_0.setLabel("1.0.0");

        Version v1_2_0 = new Version();
        v1_2_0.setId(2L);
        v1_2_0.setLabel("1.2.0");

        Version v2_1_3 = new Version();
        v2_1_3.setId(3L);
        v2_1_3.setLabel("2.1.3");

        testVersions = Arrays.asList(v1_0_0, v1_2_0, v2_1_3);
    }

    @Test
    void testDeleteVersion_Success() throws Exception {
        // Given
        Long versionIdToDelete = 2L;
        Version versionToDelete = testVersions.get(1); // 1.2.0
        Version currentVersion = testVersions.get(2); // 2.1.3 (latest)

        when(versionService.getVersionById(versionIdToDelete)).thenReturn(Optional.of(versionToDelete));
        when(versionService.getCurrentVersion()).thenReturn(currentVersion);
        when(versionService.getAllVersions()).thenReturn(testVersions);

        // When & Then
        mockMvc.perform(post("/admin/deleteVersion")
                .param("versionId", versionIdToDelete.toString())
                .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isOk())
                .andExpect(content().string("Versão 1.2.0 foi excluída com sucesso!"));

        verify(versionService).deleteVersion(versionIdToDelete);
        verify(notificationService).addNotification(anyString(), anyString(), anyString(), anyString(), isNull());
    }

    @Test
    void testDeleteVersion_VersionNotFound() throws Exception {
        // Given
        Long nonExistentVersionId = 999L;
        when(versionService.getVersionById(nonExistentVersionId)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(post("/admin/deleteVersion")
                .param("versionId", nonExistentVersionId.toString())
                .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isBadRequest())
                .andExpect(content().string("Versão com ID 999 não encontrada."));

        verify(versionService, never()).deleteVersion(anyLong());
        verify(notificationService, never()).addNotification(anyString(), anyString(), anyString(), anyString(), any());
    }

    @Test
    void testDeleteVersion_CannotDeleteCurrentVersion() throws Exception {
        // Given
        Long currentVersionId = 3L;
        Version currentVersion = testVersions.get(2); // 2.1.3

        when(versionService.getVersionById(currentVersionId)).thenReturn(Optional.of(currentVersion));
        when(versionService.getCurrentVersion()).thenReturn(currentVersion);

        // When & Then
        mockMvc.perform(post("/admin/deleteVersion")
                .param("versionId", currentVersionId.toString())
                .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isBadRequest())
                .andExpect(content().string("Não é possível excluir a versão atual (2.1.3). Esta é a versão mais recente do sistema."));

        verify(versionService, never()).deleteVersion(anyLong());
        verify(notificationService, never()).addNotification(anyString(), anyString(), anyString(), anyString(), any());
    }

    @Test
    void testDeleteVersion_CannotDeleteOnlyVersion() throws Exception {
        // Given
        Long versionId = 1L;
        Version onlyVersion = testVersions.get(0);
        List<Version> singleVersionList = Arrays.asList(onlyVersion);

        when(versionService.getVersionById(versionId)).thenReturn(Optional.of(onlyVersion));
        when(versionService.getCurrentVersion()).thenReturn(null); // No current version to avoid current version check
        when(versionService.getAllVersions()).thenReturn(singleVersionList);

        // When & Then
        mockMvc.perform(post("/admin/deleteVersion")
                .param("versionId", versionId.toString())
                .contentType(MediaType.APPLICATION_FORM_URLENCODED))
                .andExpect(status().isBadRequest())
                .andExpect(content().string("Não é possível excluir a única versão do sistema. Deve existir pelo menos uma versão."));

        verify(versionService, never()).deleteVersion(anyLong());
        verify(notificationService, never()).addNotification(anyString(), anyString(), anyString(), anyString(), any());
    }
}
