# Notification API Guide

## 1. Create Notification Endpoint

**URL:** `POST /api/notifications`

**Content-Type:** `application/json`

### Request Body Structure

```json
{
    "type": "SYSTEM" | "USER",
    "title": "string (required)",
    "subtitle": "string (required)", 
    "img": "string (optional)",
    "userId": "integer (required for USER type)"
}
```

### Examples

#### 1. Create System Notification (for all users)

```json
{
    "type": "SYSTEM",
    "title": "System Maintenance",
    "subtitle": "The system will be under maintenance from 2 AM to 4 AM",
    "img": "maintenance-icon.png"
}
```

#### 2. Create User-Specific Notification

```json
{
    "type": "USER",
    "title": "Welcome Message",
    "subtitle": "Welcome to the platform! Please complete your profile.",
    "img": "welcome-icon.png",
    "userId": 1
}
```

### Response Format

#### Success Response
```json
{
    "success": true,
    "message": "System notification created successfully for all users",
    "notificationId": null
}
```

#### Error Response
```json
{
    "success": false,
    "message": "Invalid notification type. Must be 'SYSTEM' or 'USER'",
    "notificationId": null
}
```

### Validation Rules

1. **type**: Must be either "SYSTEM" or "USER"
2. **title**: Required, cannot be blank
3. **subtitle**: Required, cannot be blank
4. **img**: Optional
5. **userId**: Required for USER type notifications, must reference an existing user

### Testing with Postman

1. Set method to `POST`
2. Set URL to `http://localhost:8080/api/notifications` (adjust port as needed)
3. Set Headers:
   - `Content-Type: application/json`
4. Set Body to raw JSON with one of the examples above
5. Send the request

### Notes

- SYSTEM notifications are automatically sent to all users in the system
- USER notifications are sent only to the specified user
- The notification will appear in the user's notification list with status "new"
- Users can mark notifications as read or archived through the UI

---

## 2. Get All Notifications Endpoint

**URL:** `GET /api/notifications`

**Description:** Retrieves all notifications in the system with their IDs for deletion purposes.

### Response Format

```json
[
    {
        "id": 1,
        "type": "SYSTEM",
        "title": "System Maintenance",
        "subtitle": "The system will be under maintenance from 2 AM to 4 AM",
        "img": "maintenance-icon.png",
        "time": "2024-01-15T10:30:00.000+00:00"
    },
    {
        "id": 2,
        "type": "USER",
        "title": "Welcome Message",
        "subtitle": "Welcome to the platform!",
        "img": "welcome-icon.png",
        "time": "2024-01-15T09:15:00.000+00:00"
    }
]
```

### Testing with Postman

1. Set method to `GET`
2. Set URL to `http://localhost:8080/api/notifications`
3. Send the request

---

## 3. Delete Notification Endpoint

**URL:** `DELETE /api/notifications/{id}`

**Description:** Deletes a notification by ID. Handles SYSTEM and USER notifications appropriately.

### URL Parameters

- `id` (required): The notification ID to delete

### Query Parameters

- `userId` (optional): For USER notifications, specify which user's notification to delete. If not provided, deletes for all users.

### Examples

#### Delete System Notification (affects all users)
```
DELETE /api/notifications/1
```

#### Delete User Notification for specific user
```
DELETE /api/notifications/2?userId=5
```

#### Delete User Notification for all users
```
DELETE /api/notifications/2
```

### Response Format

#### Success Response
```json
{
    "success": true,
    "message": "System notification deleted successfully for all users",
    "notificationType": "SYSTEM",
    "deletedCount": 15
}
```

#### Error Response
```json
{
    "success": false,
    "message": "Notification not found",
    "notificationType": null,
    "deletedCount": 0
}
```

### Deletion Logic

1. **SYSTEM Notifications**: Always deleted for all users
2. **USER Notifications**:
   - If `userId` is provided: Deletes only for that specific user
   - If `userId` is not provided: Deletes for all users who have the notification
   - If it was the last user notification, the notification itself is also deleted

### Testing with Postman

1. Set method to `DELETE`
2. Set URL to `http://localhost:8080/api/notifications/{id}` (replace {id} with actual ID)
3. Optionally add query parameter `userId` for USER notifications
4. Send the request

---

## Complete API Workflow

1. **Create notifications** using `POST /api/notifications`
2. **List all notifications** using `GET /api/notifications` to get IDs
3. **Delete specific notifications** using `DELETE /api/notifications/{id}`

### Notes
