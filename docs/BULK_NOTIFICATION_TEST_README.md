# Bulk Notification Test Guide

## Overview

The `NotificationBulkInsertTest` class provides a configurable way to create large numbers of notifications for testing purposes. It supports both transactional (safe) and definitive (permanent) modes.

## Features

- ✅ **Configurable Count**: Adjust the number of notifications to create
- ✅ **Transactional Mode**: Safe testing with automatic rollback
- ✅ **Definitive Mode**: Create permanent notifications for real testing
- ✅ **User Targeting**: Support for both SYSTEM and USER notifications
- ✅ **Real User Assignment**: Assign notifications to specific existing users
- ✅ **Progress Logging**: Configurable batch progress reporting
- ✅ **Auto User Creation**: Automatically create test users if needed

## Configuration

### Quick Setup

Add these properties to your `application-test.properties`:

```properties
# Basic configuration for 1000 transactional notifications
test.bulk.notification-count=1000
test.bulk.transactional=true
test.bulk.system-notifications=true
```

### All Configuration Options

| Property | Default | Description |
|----------|---------|-------------|
| `test.bulk.notification-count` | 1000 | Number of notifications to create |
| `test.bulk.batch-size` | 100 | Progress logging interval |
| `test.bulk.log-progress` | true | Whether to show progress logs |
| `test.bulk.create-test-users` | true | Create test users if none exist |
| `test.bulk.test-user-count` | 5 | Number of test users to create |
| `test.bulk.transactional` | true | Transactional (rollback) vs definitive (permanent) |
| `test.bulk.real-user-email` | null | Email of real user for USER notifications |
| `test.bulk.system-notifications` | true | SYSTEM vs USER notification type |

## Usage Examples

### 1. Safe Testing (Recommended)

```properties
# Creates 500 transactional notifications that will be rolled back
test.bulk.notification-count=500
test.bulk.transactional=true
test.bulk.system-notifications=true
```

### 2. Create Permanent System Notifications

```properties
# Creates 100 permanent SYSTEM notifications for all users
test.bulk.notification-count=100
test.bulk.transactional=false
test.bulk.system-notifications=true
```

### 3. Create Notifications for Specific User

```properties
# Creates 50 permanent USER <NAME_EMAIL>
test.bulk.notification-count=50
test.bulk.transactional=false
test.bulk.system-notifications=false
test.bulk.real-user-email=<EMAIL>
```

### 4. Large Scale Performance Testing

```properties
# Creates 10,000 transactional notifications with detailed logging
test.bulk.notification-count=10000
test.bulk.batch-size=500
test.bulk.transactional=true
test.bulk.log-progress=true
```

## Running the Tests

### Using IDE

1. Open `NotificationBulkInsertTest.java`
2. Configure properties in `application-test.properties`
3. Run `testBulkInsertSystemNotifications()` method

### Using Maven

```bash
# Run all notification tests
mvn test -Dtest=NotificationBulkInsertTest

# Run specific test method
mvn test -Dtest=NotificationBulkInsertTest#testBulkInsertSystemNotifications
```

### Using Gradle

```bash
# Run all notification tests
./gradlew test --tests NotificationBulkInsertTest

# Run specific test method
./gradlew test --tests NotificationBulkInsertTest.testBulkInsertSystemNotifications
```

## Safety Features

### Transactional Mode (Default)
- ✅ **Safe**: All records are automatically rolled back after test
- ✅ **No Cleanup**: No manual cleanup required
- ✅ **Repeatable**: Can run multiple times without side effects

### Definitive Mode (Use with Caution)
- ⚠️ **Permanent**: Records are saved to the database permanently
- ⚠️ **Manual Cleanup**: You may need to clean up records manually
- ⚠️ **Production Risk**: Never use on production databases

## Sample Output

```
[BULK_INSERT_TEST] Starting TRANSACTIONAL bulk insert of 1000 notifications...
[BULK_INSERT_TEST] Initial notification count: 0
[BULK_INSERT_TEST] Found 3 users in the system
[BULK_INSERT_TEST] Created 100/1000 notifications...
[BULK_INSERT_TEST] Created 200/1000 notifications...
...
[BULK_INSERT_TEST] ===== BULK INSERT COMPLETED =====
[BULK_INSERT_TEST] Mode: TRANSACTIONAL (will rollback)
[BULK_INSERT_TEST] Type: SYSTEM for all users
[BULK_INSERT_TEST] Initial count: 0
[BULK_INSERT_TEST] Final count: 1000
[BULK_INSERT_TEST] Created: 1000 notifications
[BULK_INSERT_TEST] Target: 1000 notifications
[BULK_INSERT_TEST] Duration: 2543ms (2.543 seconds)
[BULK_INSERT_TEST] Average: 2.543ms per notification
```

## Notification Content

The test creates realistic notifications with:
- **Varied Titles**: 20 different system notification titles
- **Varied Subtitles**: 15 different notification subtitles  
- **Varied Icons**: 20 different Tabler icons
- **Sequential Numbering**: Each notification gets a unique number

## Best Practices

1. **Start Small**: Begin with 100-500 notifications for initial testing
2. **Use Transactional Mode**: Always use transactional mode unless you specifically need permanent records
3. **Monitor Performance**: Watch the average time per notification for performance insights
4. **Verify Users**: Ensure you have users in the system before creating USER notifications
5. **Clean Up**: If using definitive mode, plan for cleanup of test data

## Troubleshooting

### No Users Found
If you see "No users found", the test will automatically create test users if `test.bulk.create-test-users=true`.

### Real User Not Found
If the specified `real-user-email` doesn't exist, the test will fall back to SYSTEM notifications.

### Performance Issues
If notifications are taking too long to create:
- Reduce `notification-count`
- Increase `batch-size` for less frequent logging
- Check database performance

### Memory Issues
For very large counts (>10,000), consider:
- Running in smaller batches
- Increasing JVM heap size
- Using definitive mode to avoid transaction overhead
