# Version Delete API Documentation

## Overview
This document describes the version deletion endpoint that allows administrators to delete application versions.

## Endpoint

### Delete Version
**POST** `/admin/deleteVersion`

Deletes an application version by ID. Only available for users with `ROLE_ADMIN`.

#### Parameters
- `versionId` (required): Long - The ID of the version to delete

#### Request Example
```bash
curl -X POST "http://localhost:8080/admin/deleteVersion" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "versionId=2"
```

#### Response Examples

**Success (200 OK)**
```
Versão 1.2.0 foi excluída com sucesso!
```

**Version Not Found (400 Bad Request)**
```
Versão com ID 999 não encontrada.
```

**Cannot Delete Current Version (400 Bad Request)**
```
Não é possível excluir a versão atual (2.1.3). Esta é a versão mais recente do sistema.
```

**Cannot Delete Only Version (400 Bad Request)**
```
Não é possível excluir a única versão do sistema. Deve existir pelo menos uma versão.
```

**Access Denied (403 Forbidden)**
```json
{
  "timestamp": "2024-01-01T12:00:00.000+00:00",
  "status": 403,
  "error": "Forbidden",
  "message": "Access Denied",
  "path": "/admin/deleteVersion"
}
```

## Business Rules

1. **Admin Only**: Only users with `ROLE_ADMIN` can delete versions
2. **Cannot Delete Current Version**: The latest (current) version cannot be deleted
3. **Cannot Delete Only Version**: At least one version must exist in the system
4. **Notification**: A system notification is created when a version is successfully deleted
5. **Logging**: All deletion attempts are logged for audit purposes

## Security

- Endpoint is protected by Spring Security
- Requires `ROLE_ADMIN` authority
- CSRF protection is disabled for admin endpoints
- All requests are logged for security auditing

## Related Endpoints

- `GET /admin/versions` - Get all versions (sorted by semantic versioning)
- `GET /admin/latest-version` - Get the latest version
- `POST /admin/addVersion` - Add a new version

## Error Handling

The endpoint includes comprehensive error handling for:
- Invalid version IDs
- Attempting to delete the current version
- Attempting to delete the only version in the system
- Database errors
- Authorization failures

## Testing

Unit tests are available in:
- `AdminControllerDeleteVersionTest.java` - Controller-level tests
- `VersionServiceTest.java` - Service-level tests

## Notifications

When a version is successfully deleted, a system notification is created with:
- **Type**: SYSTEM
- **Title**: "Versão removida"
- **Subtitle**: "A versão {version_label} foi removida do sistema."
- **Icon**: Trash icon with warning color
