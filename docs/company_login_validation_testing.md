# Company Login Validation Testing Guide

## Overview
This document describes how to test the new company-level login validation feature that prevents users from logging in if their company is disabled or locked.

## Feature Description
The system now validates the company status during user authentication:
- If a user's company is **disabled**, login is denied with an appropriate message
- If a user's company is **locked**, login is denied with an appropriate message
- Users without a company assigned can still login (backward compatibility)
- Users from companies that don't exist in the database can still login (backward compatibility)

## Prerequisites
1. Run the database migration script: `sql_queries/add_company_status_fields.sql`
2. Ensure you have test users assigned to different companies
3. Ensure you have companies with different status combinations

## Test Scenarios

### Scenario 1: User with Enabled and Unlocked Company
**Expected Result**: <PERSON>gin should succeed normally

**Steps**:
1. Ensure company exists with `enabled=true` and `locked=false`
2. Ensure user has `company` field set to the company name
3. Attempt to login with valid credentials
4. **Expected**: Login succeeds, user is redirected to dashboard

### Scenario 2: User with Disabled Company
**Expected Result**: Login should fail with company disabled message

**Steps**:
1. Set company status: `UPDATE job_company SET enabled=false WHERE company_name='TestCompany'`
2. Attempt to login with user assigned to 'TestCompany'
3. **Expected**: Login fails with message "Your company account (TestCompany) has been disabled. Please contact support for assistance."

### Scenario 3: User with Locked Company
**Expected Result**: Login should fail with company locked message

**Steps**:
1. Set company status: `UPDATE job_company SET locked=true WHERE company_name='TestCompany'`
2. Attempt to login with user assigned to 'TestCompany'
3. **Expected**: Login fails with message "Your company account (TestCompany) has been temporarily locked. Please contact support for assistance."

### Scenario 4: User with Both Disabled and Locked Company
**Expected Result**: Login should fail with disabled message (checked first)

**Steps**:
1. Set company status: `UPDATE job_company SET enabled=false, locked=true WHERE company_name='TestCompany'`
2. Attempt to login with user assigned to 'TestCompany'
3. **Expected**: Login fails with disabled message (disabled check takes precedence)

### Scenario 5: User with No Company Assigned
**Expected Result**: Login should succeed (backward compatibility)

**Steps**:
1. Ensure user has `company` field set to NULL or empty string
2. Attempt to login with valid credentials
3. **Expected**: Login succeeds normally

### Scenario 6: User with Non-existent Company
**Expected Result**: Login should succeed (backward compatibility)

**Steps**:
1. Set user's company field to a company name that doesn't exist in database
2. Attempt to login with valid credentials
3. **Expected**: Login succeeds normally (with warning logged)

## Testing Both Login Methods

### Web Login (Form-based)
- Access `/login` page
- Enter credentials and submit form
- Check for error messages displayed on the login page

### API Login (JWT-based)
- POST to `/auth/login` with JSON credentials
- Check HTTP response status and error message in response body

## Database Setup for Testing

```sql
-- Create test company
INSERT INTO job_company (company_name, address, country, tax_number, enabled, locked) 
VALUES ('TestCompany', '123 Test St', 'TestCountry', 'TAX123', true, false);

-- Create test user assigned to company
INSERT INTO job_users (full_name, email, password, company, enabled) 
VALUES ('Test User', '<EMAIL>', '$2a$10$...', 'TestCompany', true);

-- Test scenarios by updating company status
UPDATE job_company SET enabled=false WHERE company_name='TestCompany';  -- Disable company
UPDATE job_company SET locked=true WHERE company_name='TestCompany';    -- Lock company
UPDATE job_company SET enabled=true, locked=false WHERE company_name='TestCompany'; -- Reset
```

## Internationalization Testing

Test the feature with different locales:
- English (default): `/login?lang=en`
- Portuguese: `/login?lang=pt`

Verify that error messages are properly translated in each language.

## Logging Verification

Check application logs for proper logging:
- Successful company validation: DEBUG level
- Company validation failures: WARN level
- Company not found: WARN level
- Validation errors: ERROR level

## Security Considerations

- Company validation occurs AFTER successful credential verification
- Failed login attempts are still tracked for rate limiting
- Company status is checked on every login (no caching)
- Users are immediately logged out if their company status changes during session

## Rollback Plan

If issues occur, you can disable the feature by:
1. Setting all companies to enabled: `UPDATE job_company SET enabled=true, locked=false`
2. Or temporarily commenting out the `validateCompanyStatus(authenticatedUser)` call in `AuthenticationService.authenticate()`
