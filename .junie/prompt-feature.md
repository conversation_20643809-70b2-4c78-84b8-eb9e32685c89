# User Management Page Implementation Request

## Objective
Create a comprehensive user management page with CRUD operations following our application's design patterns and best practices. The page should allow administrators to manage users who have access to the system.

## Design Reference
Please implement the page based on the design at: https://demos.pixinvent.com/vuexy-html-admin-template/html/vertical-menu-template/app-user-list.html

## Technical Requirements
1. **Feature-based organization**: Follow our package-by-feature structure as shown in `.junie/guidelines.md`
2. **HTMX Integration**: Use HTMX for all dynamic behaviors instead of explicit JavaScript, similar to our existing pages like `user/edituser.html` and `user/fragments/recent-activity-list.html`
3. **Pagination**: Implement server-side pagination similar to `securityUserPaged` in `UserPageController.java`
4. **Filtering**: Add filtering capabilities for the user list (by name, email, role, etc.)
5. **REST API**: Create proper REST endpoints with pagination, following patterns in `UsersController.java`
6. **OpenAPI Documentation**: Document all REST endpoints with Swagger annotations
7. **Validation**: Implement proper validation using Bean Validation (JSR-380)
8. **DTOs as Records**: Use Java records for all DTOs similar to `UpdateUserDetailsDto.java`
9. **Security**: Ensure proper access control (admin-only) like in `SecurityConfiguration.java`
10. **Internationalization**: Use message bundles for all UI text to support multiple languages

## Required Features
1. **User Listing**: Paginated table with sorting and filtering
2. **User Creation**: Form to add new users with validation
3. **User Editing**: Form to modify existing user details
4. **User Deletion**: Confirmation dialog before deletion
5. **Role Management**: Ability to assign/change user roles

## Implementation Details
- Create necessary controller(s), service(s), and repository methods
- Implement Thymeleaf templates with HTMX attributes for dynamic content loading
- Follow the existing UI patterns from our other admin pages
- Ensure all operations are properly secured for admin access only
- Add appropriate error handling and user feedback
- **Internationalization Requirements**:
    - Add all UI text to message bundles (`messages.properties`, `messages_en_US.properties`, `messages_pt_BR.properties`)
    - Use Thymeleaf's `th:text="#{key}"` syntax for all visible text
    - Follow existing message key patterns (e.g., `user.management.title`, `user.field.email`)
    - Ensure all validation messages, success/error notifications, and UI elements are internationalized
    - Support language switching similar to existing pages
    - **Always use `th:lang="${#locale.language}"` in HTML files** instead of hardcoded language attributes to ensure proper language switching

## HTML Template Requirements
- All HTML files must use `th:lang="${#locale.language}"` in the html tag to dynamically set the page language
- Example: `<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org">`
- This ensures the page language changes based on the user's selected locale
- Follow the pattern in existing templates like `index.html`, `layout.html`, and `user/dashboard.html`

## Message Bundle Keys
Add the following message keys (and any others needed) to all language files:
- `user.management.title` - Main page title
- `user.management.subtitle` - Page subtitle/description
- `user.create.title` - Create user form title
- `user.edit.title` - Edit user form title
- `user.field.*` - All form field labels
- `user.button.*` - All button labels
- `user.filter.*` - All filter labels
- `user.validation.*` - All validation messages
- `user.notification.*` - All notification messages

## References
Please reference these existing files for patterns:
- `src/main/java/ag/fuel/jobify/user/controller/UserPageController.java` for controller patterns
- `src/main/java/ag/fuel/jobify/user/dto/UpdateUserDetailsDto.java` for DTO patterns
- `src/main/resources/templates/user/fragments/recent-activity-list.html` for HTMX pagination
- `src/main/resources/templates/user/edituser.html` for form handling with HTMX
- `src/main/java/ag/fuel/jobify/company/controller/CompanyController.java` for admin page patterns
- `src/main/resources/messages.properties`, `messages_en_US.properties`, and `messages_pt_BR.properties` for message bundle patterns
- `src/main/java/ag/fuel/jobify/common/config/LocaleConfiguration.java` for locale configuration