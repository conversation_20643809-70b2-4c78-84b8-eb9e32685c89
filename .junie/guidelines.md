This file contains guidelines for <PERSON><PERSON> to follow when working on this SpringBoot project. Adhering to these standards ensures consistency.

## Core Technologies & versions

* **Java:** Use the latest Long-Term Support (LTS) version of Java (e.g., Java 17 or later) unless project constraints dictate otherwise.
* **Spring Boot:** Always use the latest stable release of Spring Boot 3.x (or the latest major stable version available) for new features
* **Build Tools:** Use Maven as the build tool. Ensure the 'pom.xml' uses the latest stable Spring Boot parent POM and compatible plugin version. Ensure the pom.xml file contains the correct configuration for MapStruct and Lombok to work together.

## Project Structure
* **Packaging:** Strongly prefer a **package-by-feature** structure over package-by-layer. This means grouping all code related to a specific feature or domain concept in the same package.

  * **Example:**
    ** PREFER THIS (Package-by-feature):**

    com.example.application
    |-- posts 						# Feature: Posts
    |	|-- PostController.java		# Controller for Posts
  * |   |-- PostService.java		# Service logic for Posts
    |	|-- PostRepository.java     # Data access for Posts
    |	|-- Post.java				# Domain / Entity for Posts
  * |   |__ dto                     # Data Transfer Objects for Posts
    |       |-- PostCreateRequest.java
    |		|-- PostSummaryResponse.java
    |
    |
    |-- users						# Feature: Users
    |	|-- UserController.java		
  * |   |-- UserService.java
    |	|-- UserRepository.java
    |	|-- User.java
    |
    |-- common                     # Optional: Truly shared utilities/config
    |   |__ exception
    |       |__ ResourceNotFoundException.java
    ...

* **AVOID THIS (Package-by-layer):** 

    com.example.application
    |-- controller
    |   |-- PostController.java
    |   |-- UserController.java
    |
    |-- service
    |   |-- PostService.java
    |   |-- UserService.java
    |
    |-- repository
    |   |-- PostRepository.java
    |   |-- UserRepository.java
    |
    |-- model
        |-- Post.java
        |-- User.java

## Data Access
* **Simple Applications/Queries:** For applications primarily dealing with straightforward, single entity operations, consider using Spring's JdbcClient for better performance.
* **Standard/Complex Applications:** For applications with domain models involving relationships, complex queries, or where JPA features like caching and lazy loading are beneficial, use Spring Data JPA.
* **Default:** If unsure, lean towards Spring Data JPA for typical application development, but use 'JdbcClient' when the overhead of JPA is too high.

## HTTP Clients
* **Outgoing HTTP Requests:** Use the Spring Framework 6+ **'RestClient'** for making synchronous or asynchronous HTTP calls.

## REST Controllers
* **Limit Records Returned:** Don't create controllers with methods where clients can retrieve unlimited number of records. Use pageable option.
* **Documentation:** Add Swagger OpenAPI documentation for all REST controller methods exposed.
* **Response Entities:** Use ResponseEntity to provide full control over the HTTP response.
* **Request Mapping:** Use specific HTTP method annotations (@GetMapping, @PostMapping, etc.) instead of @RequestMapping with method attribute.
* **Path Variables:** Use @PathVariable for RESTful resource identifiers and @RequestParam for query parameters.
* **Content Negotiation:** Support multiple content types where appropriate (JSON, XML, etc.).
* **Versioning:** Use versioning in the APIs exposed.

## Java Language Features
* **Data Carriers:** Use Java **records** ('record') for immutable data transfer objects (DTOs), value objects, or simple data aggregates.
* **New object construction:** Use Builder pattern wherever possible.
* **Immutability:** Favour immutability of objects where appropriate, especially for DTO's and configuration properties.
* **Optional:** Use Java's Optional for values that might be null, especially in service return types.
* **Stream API:** Leverage Stream API for collection processing as demonstrated in the ImageGenerationService.
* **Functional Interfaces:** Use functional interfaces and lambda expressions for cleaner code.
* **Pattern Matching:** Utilize pattern matching for instanceof (Java 16+) where applicable.

## Spring Framework Best Practices
* **Dependency Injection:** Use **constructor injection** for mandatory dependencies. Avoid field injection. Use Lombok '@RequiredArgsConstructor' annotation.
* **Configuration:** Use a 'config' package and 'application.yaml' properties file for application properties and variables that can be changed.
* **Error Handling:** Implement consistent exception handling, potentially using '@ControllerAdvice' and custom exception classes.
* **Logging:** Use Slf4j with a suitable backend for logging.
* **Service Layer:** Keep business logic in service classes, separate from controllers.
* **Validation:** Use Bean Validation (JSR-380) with annotations like @Valid and @NotNull.
* **Profiles:** Utilize Spring profiles for environment-specific configurations.
* **Actuator:** Enable Spring Boot Actuator for monitoring and health checks in production environments.
* **Metrics:** Add metrics to the controller classes and other places where applicable

### Database Table Naming Convention

When creating new entities, the corresponding database table name must be prefixed with `job_`. This ensures consistency and clear differentiation between application-specific tables and other system tables.

**Example:**
- `job_users`
- `job_orders`
- `job_products`


## AI Integration
* **Client Configuration:** Use dependency injection for AI service clients and store API keys in environment variables or secure configuration.
* **Error Handling:** Implement robust error handling for AI service calls with appropriate fallbacks and retry mechanisms.
* **Response Processing:** Use Java records for AI response data structures as seen in the ImageGenerationService.
* **Async Processing:** Consider using CompletableFuture for long-running AI operations to improve responsiveness.

## Security
* **Input Validation:** Always validate and sanitize user inputs, especially when used in AI prompts or external service calls.
* **API Keys:** Never hardcode API keys or secrets. Use environment variables, Spring's @Value annotation, or secure vaults.
* **Rate Limiting:** Implement rate limiting for endpoints that consume external API resources or are computationally expensive.
* **Content Validation:** For user-generated content or AI-generated content, implement appropriate content filtering and validation.

## Exception Handling
* **Custom Exceptions:** Create domain-specific exception classes that extend RuntimeException.
* **Global Handler:** Implement a global exception handler using @ControllerAdvice to provide consistent error responses.
* **External Service Failures:** Handle external service failures gracefully with appropriate retry mechanisms and fallbacks.
* **Logging:** Log exceptions with appropriate context information while avoiding sensitive data exposure.

## Testing
* **Unit Tests:** Write unit tests for services and components using JUnit 5 and Mockito.
* **Integration Tests:** Write integration tests using '@SpringBootTest'. For database interactions use TestContainers.
* **Test Location:** Place tests in the standard 'src/test/java' directory, mirroring the source package structure.

## General Code Quality
* **Readability:** Write clean, readable, and maintainable code.
* **Comments:** Add comments only where necessary to explain complex logic or non-obvious decisions. Code should be self-documenting wherever possible.
* **API Design:** Design RESTful APIs with clear resource naming, proper HTTP methods and consistent request/response formats. Please use appropriate API versioning for all REST APIs.

### Frontend Interaction Guidelines

To ensure seamless integration and maintain a declarative approach, all dynamic behaviors should be implemented using **HTMX** rather than explicit JavaScript. This helps simplify development, reduce unnecessary complexity, and improve maintainability.

**Rules:**
- **Prefer HTMX attributes** over manually writing JavaScript functions for event handling.
- **Leverage built-in HTMX extensions** for common use cases like AJAX requests, animations, and WebSocket interactions.
- **Avoid inline JavaScript manipulation** unless strictly necessary.
- **Ensure server responses are optimized** for partial updates using HTML fragments.

**Example:**
Instead of writing:
```html
<button onclick="fetchData()">Click Me</button>
```
Prefer:
```html
<button hx-get="/data" hx-trigger="click">Click Me</button>
```

## Use of Maven
* **Maven Wrapper:** Always user the Maven wrapper to run Maven commands in the terminal