# Codebase Refactoring Request

## Objective
Please refactor my SpringBoot application following the guidelines in `.junie/guidelines.md`. Focus on improving code quality, maintainability, and adherence to best practices.

## Priority Areas
1. Convert package structure from layer-based to feature-based organization
2. Ensure proper dependency injection using constructor injection with Lombok's @RequiredArgsConstructor
3. Implement proper error handling with custom exceptions and global exception handlers
4. Update REST controllers to follow best practices (pagination, proper HTTP methods, ResponseEntity)
5. Apply Java modern features (records for DTOs, Optional, Stream API)
6. Ensure security best practices are followed

## Specific Requests
- Identify any controllers returning unlimited records and convert them to use pagination
- Add OpenAPI documentation to REST endpoints
- Convert DTOs to Java records where appropriate
- Implement proper validation using Bean Validation (JSR-380)
- Ensure all external HTTP calls use Spring's RestClient
- Apply proper versioning to all REST APIs
- Make sure you fix all the thymeleaf templates to work with the new changes
- Make sure you fix all the tests to work with the new changes

## Output Format
For each file you refactor, please:
1. Explain the changes made and why they align with the guidelines
2. Highlight any potential issues or edge cases to be aware of
3. Provide the refactored code

Thank you!