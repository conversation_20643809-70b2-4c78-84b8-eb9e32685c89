-- Migration script to add enabled and locked fields to company table
-- This script adds status management capabilities to companies

-- Add enabled column (default to true for existing companies)
ALTER TABLE job_company
ADD enabled BIT NOT NULL DEFAULT 1;

-- Add locked column (default to false for existing companies)
ALTER TABLE job_company
ADD locked BIT NOT NULL DEFAULT 0;

-- Update existing companies to be enabled and unlocked by default
UPDATE job_company
SET enabled = 1, locked = 0 
WHERE enabled IS NULL OR locked IS NULL;

-- Add comments to document the new columns
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Whether the company is enabled/active (1 = TRUE, 0 = FALSE)',
    @level0type = N'Schema', @level0name = 'dbo',
    @level1type = N'Table',  @level1name = 'job_company',
    @level2type = N'Column', @level2name = 'enabled';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Whether the company is locked/suspended (1 = TRUE, 0 = FALSE)',
    @level0type = N'Schema', @level0name = 'dbo',
    @level1type = N'Table',  @level1name = 'job_company',
    @level2type = N'Column', @level2name = 'locked';

-- Verify the changes
SELECT 
    id,
    company_name,
    enabled,
    locked,
    created_at,
    updated_at
FROM job_company
ORDER BY company_name;
