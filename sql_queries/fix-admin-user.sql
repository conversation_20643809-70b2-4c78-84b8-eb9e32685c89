-- <PERSON><PERSON>t to check and fix admin user issues
-- Replace '<EMAIL>' with your actual email

-- 1. Check if user exists and their status
SELECT 
    id, 
    email, 
    full_name, 
    enabled, 
    account_locked, 
    failed_attempt
FROM job_users
WHERE email = '<EMAIL>';

-- 2. Check user roles
SELECT 
    u.email, 
    r.name as role_name
FROM job_users u
JOIN job_user_roles ur ON u.id = ur.user_id
JOIN job_roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';

-- 3. Enable the user if not enabled
UPDATE job_users
SET enabled = true 
WHERE email = '<EMAIL>' AND enabled = false;

-- 4. Unlock account if locked
UPDATE job_users
SET account_locked = false, 
    lock_time = NULL, 
    failed_attempt = 0 
WHERE email = '<EMAIL>' AND account_locked = true;

-- 5. Add ADMIN role if not present (get user ID first)
-- First, check if ROLE_ADMIN exists
SELECT * FROM job_roles WHERE name = 'ROL<PERSON>_ADMIN';

-- If ROLE_ADMIN doesn't exist, create it
INSERT INTO job_roles (name)
SELECT 'ROLE_ADMIN' 
WHERE NOT EXISTS (SELECT 1 FROM job_roles WHERE name = 'ROLE_ADMIN');

-- Add ADMIN role to user (replace with your actual user email)
INSERT INTO job_user_roles (user_id, role_id)
SELECT u.id, r.id
FROM job_users u, job_roles r
WHERE u.email = '<EMAIL>' 
  AND r.name = 'ROLE_ADMIN'
  AND NOT EXISTS (
    SELECT 1 FROM job_user_roles ur
    WHERE ur.user_id = u.id AND ur.role_id = r.id
  );

-- 6. Verify the changes
SELECT 
    u.email, 
    u.enabled,
    u.account_locked,
    r.name as role_name
FROM job_users u
LEFT JOIN job_user_roles ur ON u.id = ur.user_id
LEFT JOIN job_roles r ON ur.role_id = r.id
WHERE u.email = '<EMAIL>';
